import config from '@/config/index'
import request from '@/utils/request/index'

const { VITE_JZT_API } = config

// ===================创建开始===================

/* 创建综合任务(名称) */
export function createScheduleName(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/schedule/create`, data)
}

/* 获取综合任务详情(名称) */
export function getScheduleDetail(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/detail`, data)
}

/* 编辑综合任务(名称) */
export function editcheduleName(data?) {
  return request.put(`${VITE_JZT_API}/tutoring/admin/task/schedule/edit`, data)
}

/* 综合任务发布 */
export function publishSchedule(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/schedule/publish`, data)
}

/* 任务组列表 (ComprehensiveTaskCreate 列表) */
export function taskGroupList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/list`, data)
}

/* 创建任务组 (comprehensiveTaskPackage 名称和时间) */
export function createTaskGroup(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/create`, data)
}

/* 任务组详情 (ComprehensiveTaskCreate 名称和时间) */
export function getTaskGroupDetail(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/detail`, data)
}

/* 编辑任务组 (ComprehensiveTaskCreate 名称和时间) */
export function editTaskGroup(data?) {
  return request.put(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/edit`, data)
}

/* 任务模式列表 */
export function openTaskPattern(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/pattern`, data)
}

/* 科目列表 */
export function taskGroupSubList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/subject/list`, data)
}

/* 任务排序 */
export function taskGroupTaskSort(data?) {
  return request.put(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/subject/task/sort`, data)
}
/* 删除任务组 */
export function deleteTaskGroup(data?) {
  return request.delete(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/delete`, data)
}

/* 关联/取消关联任务组 */
export function taskGroupTaskToggle(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/toggle`, data)
}
/* 任务组排序 */
export function taskGroupSort(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/sort`, data)
}

/* 通关任务组 */
export function taskGroupTaskComplete(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/complete`, data)
}
/* 删除任务(单个) */
export function deleteTask(data?) {
  return request.delete(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/task/delete`, data)
}

// ===================创建结束===================

// ===================综合任务/任务组详情开始===================

/* 综合任务状态列表 */
export function scheduleStateList() {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/state/list`, {}, {
    delay: false,
  })
}

/* 综合任务列表 */
export function getScheduleData(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/page/list`, data, {
    replace: true,
  })
}

/* 关闭综合任务 */
export function scheduleClose(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/schedule/close`, data, {
    delay: false,
  })
}

/* 任务完成状态列表 */
export function taskStateList() {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/task/state/list`, {}, {
    delay: false,
  })
}

/* 任务批改状态列表 */
export function taskCorrectionList() {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/task/correction/state/list`, {}, {
    delay: false,
  })
}

/* 科目列表 */
export function getSubjectList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/subject/select/list`, data, {
    delay: false,
    replace: true,
  })
}

/* 获取任务组 */
export function getGroupList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/select/list`, data, {
    delay: false,
  })
}

/* 获取任务组学生列表 */
export function getStudentList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/student/task/list`, data, {
    replace: true,
  })
}

/* 手动通关 */
export function studentComplete(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/student/task/complete
`, data, {
    delay: false,
    replace: true,
  })
}

/* 一键通关 */
export function studentAllComplete(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/schedule/group/complete
`, data, {
    delay: false,
    replace: true,
  })
}

// ===================综合任务/任务组详情结束===================
// 同期学生列表和批改状态
export function getStudentsApi(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/scheduleGroupStudent/getTaskScheduleGroupStudentList`, data, {
    replace: true,
  })
}
// 学生信息
export function getStudentDetailApi(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/scheduleGroupStudent/getTaskScheduleGroupStudentDetail`, data, {
    replace: true,
  })
}
// 查询学生任务作答列表
export function getAnswerListApi(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/scheduleGroupStudent/getTaskScheduleGroupStudentAnswerDetail`, data, {
    replace: true,
  })
}

// ===================资源任务配置===================
/* 任务模式列表 */
export function getTaskPattern(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/pattern`, data, {
    delay: false,
    replace: true,
  })
}
/* 查询当前登录教师下文件夹列表 */
export function getFolderList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/resourceTeacherDir/getList`, data, {
    delay: false,
    replace: true,
  })
}
/* 新增教师资源目录 */
export function addFolder(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/resourceTeacherDir/save`, data, {
    delay: false,
    replace: true,
  })
}
/* 删除教师资源目录 */
export function deleteFolder(data?) {
  return request.delete(`${VITE_JZT_API}/tutoring/admin/task/resourceTeacherDir/delete`, data, {
    delay: false,
    replace: true,
  })
}
/* 文件夹拖拽排序 */
export function sortFolder(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/resourceTeacherDir/sort`, data, {
    delay: false,
    replace: true,
  })
}
/* 查询指定文件夹下面的资源列表 */
export function getTaskResourceTeacherUploadList(data?) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/resourceTeacherUpload/getTaskResourceTeacherUploadList`, data, {
    delay: false,
  })
}
/* 上传资源 */
export function uploadResource(data?) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/resourceTeacherUpload/upload`, data, {
    delay: false,
    replace: true,
  })
}
/* 删除教师资源目录 */
export function deleteResource(data?) {
  return request.delete(`${VITE_JZT_API}/tutoring/admin/task/resourceTeacherUpload/deleteTaskResourceTeacherUploadFile`, data, {
    delay: false,
  })
}

// 教师批改
export function submitAnswerListApi(data) {
  return request.post(`${VITE_JZT_API}/tutoring/admin/task/teacherCorrection/submitTeacherCorrection`, data, {
    replace: true,
  })
}

// 删除综合任务
export function deleteComprehensiveTask(data) {
  return request.delete(`${VITE_JZT_API}/tutoring/admin/task/schedule`, data)
}
