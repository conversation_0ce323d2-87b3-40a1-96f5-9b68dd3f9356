import config from '@/config/index'
import request from '@/utils/request/index'
import { delay } from 'lodash-es'

const {
  VITE_PAD_API,
  VITE_WORKBENCH_API,
  VITE_JZT_API,
} = config

// 获取教师信息
export async function getInfo() {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/teacherInfo`,
    {},
    { delay: false },
  )
}

// 获取教师学科
export async function getSubjectList() {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/subjectSelect`,
    {},
    { delay: false },
  )
}

// 获取布置对象类型
export async function getObjectType() {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/arrangeObjectType`,
    {},
    { delay: false },
  )
}

// 获取教师班级类型类型
export async function getClassType() {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/classTypeSelect`,
    {},
    { delay: false },
  )
}

// 获取老师特定班级类型所教的科目
export async function getClassSubject(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/mySysSubject`, data, {
    delay: false,
    replace: true,
  })
}

// 获取老师所教的班级
export async function getSchoolClass(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/mySchoolClass`,
    data,
    {
      delay: false,
      replace: true,
    },
  )
}

// 获取任务类型
export async function getTaskType() {
  return request.get(
    `${VITE_JZT_API}/tutoring/common/taskType`,
    {},
    { delay: false },
  )
}

// 获取布置时间
export async function getPublishTime() {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/arrangeTime`,
    {},
    { delay: false },
  )
}

// 获取教师班级列表
export async function getTeacherClassList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/gradeClassList`, data)
}

// 获取学生列表
export async function getStudentList(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/class/studentList/search`,
    data,
  )
}

// 获取小组列表
export async function getGroupList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/group/list`, data)
}

// 获取教师班级类型
export async function getTeacherClassType(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/classTypeSelect`,
    data,
  )
}

// 获取教师上一次布置任务的班级
export async function getTeacherLastClass(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/lastTaskClass`, data)
}

// 获取任务列表
export async function getTaskList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/list`, data, {
    replace: true,
  })
}

// 删除任务
export async function deleteTask(data) {
  return request.delete(`${VITE_JZT_API}/tutoring/admin/task/delete`, data, {
    replace: true,
    delay: false,
  })
}

// 任务详情
export async function taskDetail(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/details`, data, {
    delay: false,
  })
}

// 修改任务提交
export async function updateTask(data) {
  return request.put(`${VITE_JZT_API}/tutoring/admin/task/taskModify`, data, {
    delay: false,
  })
}

export function fetchZuJuanInfoApi() {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/zuJuanInfo`)
}

// 任务删除确认
export function deleteConfirmed(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/deleteTaskConfirm`,
    data,
    { delay: false },
  )
}
export function getQuestionListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/book/questionList`,
    data,
  )
}
export function getSchoolsApi(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/schoolSelect`, data)
}
export function getGradesApi(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/gradeSelect`, data)
}
export function getBookListApi(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/book/list`, data)
}
/* 启鸣资源 查询有资源的教材版本列表 */
export function getBookVersionListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/resource/qm/textbooks`,
    data,
  )
}
/* 启鸣资源 带资源的章节树 洋葱、四中、导学案 */
export function getChapterTreeApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/resource/qm/chapterTree`,
    data,
  )
}
/* 启鸣资源 来源select */
export function getFromSelectApi() {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/resource/source`)
}
/* 启鸣资源 类型select */
export function getTypeSelectApi() {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/resource/type`)
}
/* 启鸣资源 教师年级select */
export function getTeacherGradeSelectApi(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/teacherGrades`, data)
}
/* 校本资源-教材版本列表 */
export function getSchoolBookVersionListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/resource/xbzy/textbooks`,
    data,
  )
}
/* 校本资源-章节树 */
export function getSchoolChapterTreeApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/resource/xbzy/chapterTree`,
    data,
  )
}
/* 校本资源-资源列表 */
export function getResourceListApi(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/task/resource/xbzy/resourceList`,
    data,
  )
}
export function getTreeDataApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/book/catalogTree`,
    data,
  )
}
export function getTypeListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/question/type/list`,
    data,
  )
}
export function getDifficultyApi() {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/question/difficulty/list`,
  )
}

// 获取试卷类型
export function getPaperTypeList() {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/book/paper/type`)
}

// 教师试卷学段年级
export function getPaperGradeList(data) {
  return request.get(`${VITE_JZT_API}/tutoring/admin/task/book/paper/grade`, data)
}

// 年份
export function getPaperYearList() {
  return request.get(`${VITE_JZT_API}/tutoring/common/fiveYearsAgo`)
}

// 来源列表
export function getPaperSourceList() {
  return request.get(`${VITE_JZT_API}/tutoring/common/source`)
}

// 校本练习-试卷选题-试卷列表
export function getPaperList(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/book/paper/list`,
    data,
  )
}

// 校本练习-试卷选题-试卷详情
export function getDiffData(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/book/paper/detail`,
    data,
  )
}

// 题目列表
export function getQuestionList(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/book/questionList`,
    data,
  )
}
// 新建试题任务
export function createTask(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/task/newQuestionTask`,
    data,
  )
}

// 设置任务取消布置
export function setTaskCancel() {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/task/setTaskCancelArrangement`,
  )
}

// 获取任务取消布置
export function getTaskCancel() {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/getTaskCancelArrangement`,
  )
}
// 导入学科网试题 返回bookId
export function importBookList(data, headers) {
  return request.post(
    `${VITE_JZT_API}/tutoring/api/xkwThird/paper/import`,
    data,
    headers,
  )
}
// 导入学科网组卷
export function getXkwPaperDataId(data, headers) {
  return request.post(
    `${VITE_JZT_API}/tutoring/api/xkwThird/composition/book`,
    data,
    headers,
  )
}

// 试题资源回显
export function getQuestionResource(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/questionAndResource`,
    data,
  )
}

// 发布资源任务
export function publishResourceTask(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/task/newResourceTask`,
    data,
  )
}
// 获取任务概况信息
export function getTaskProfileApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/resourceTask/report/overview`,
    data,
  )
}
// 获取任务进度总览信息
export function getTaskProgressApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/resourceTask/report/progress`,
    data,
  )
}
// 获取学生列表
export function getStudentListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/resourceTask/report/studentList`,
    data,
  )
}
// 获取班级下拉列表
export function getClassListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/exercise/exerciseSourceType/classList`,
    data,
  )
}

// 年级select
export function getGradeSelectApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/exercise/exerciseSourceType/gradeList`,
    data,
  )
}

// 班级select
export function getClassSelectApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/exercise/exerciseSourceType/classList`,
    data,
  )
}

// 学生报告总的接口
export function getStudentReportApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/exercise/report`,
    data,
  )
}

// 教师一键提醒
export function getTeacherRemindApi(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/task/notify/messages`,
    data,
  )
}

// 题目列表
export function getQuestionListDetailApi(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/exercise/report/questionList`,
    data,
    { replace: true },
  )
}

// 个人报告查看
export function getPersonalReportApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/exercise/report/student/questionResult`,
    data,
  )
}

// 学生单个题目答题结果
export function getSingleQuestionDetail(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/exercise/report/student/singleQuestionResult`,
    data,
  )
}

// 订正记录列表
export function getCorrectionDetail(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/exercise/report/student/questionAmendResult`,
    data,
  )
}

// 旧版老师入口提示
export function oldTeacherTip(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/accountManage/warn/once`,
    data,
    { delay: false },
  )
}
// 学段 select
export function getStageSelectApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/examErrorTask/stageSelect`,
    data,
    { delay: false },
  )
}
// 错题任务-年级 select
export function getErrorGradeSelectApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/examErrorTask/gradeSelect`,
    data,
    { delay: false },
  )
}
// 错题任务-考试列表
export function getErrorExamListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/examErrorTask/examPaperSelect`,
    data,
    { delay: false },
  )
}
// 错题任务-考试试题列表
export function getErrorExamQuestionListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/examErrorTask/examPaperQuestionList`,
    data,
  )
}
// 错题任务-考试试题-报告
export function getErrorExamReportApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/examErrorTask/examPaperQuestionReport`,
    data,
  )
}
// 错题任务-考试试题-学生作答详情
export function getErrorExamQuestionDetailApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/examErrorTask/examPaperQuestion/studentAnswer`,
    data,
    {
      delay: false,
      replace: true,
    },
  )
}
// 发布错题任务
export function publishErrorTaskApi(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/task/newExamErrorQuestionTask`,
    data,
  )
}
// 题目推荐列表
export function getQuestionRecommendListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/book/recommend/questionList`,
    data,
  )
}
// 书题目推荐列表 完成检查
export function getBookQuestionRecommendListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/book/recommend/status`,
    data,
  )
}

// 书题目推荐列表 完成检查
export function getCourseKnowledge(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/common/courseKnowledge`,
    data,
  )
}

// 获取老师所带班参与了该场考试的班级列表
export function getErrorExamClassListApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/getTeacherExamClassList`,
    data,
    {
      delay: false,
    },
  )
}
// 错题任务-考试科目试题试卷-新
export function getErrorExamQuestionPaperApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/examErrorTask/examPaperQuestionNewList`,
    data,
  )
}
// 获取老师针对考试的发布记录
export function getErrorExamPublishRecordApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/getTeacherTaskExamRecordList`,
    data,
  )
}
// 错题任务-获取考试题目过滤范围
export function getErrorExamQuestionFilterRangeApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/examErrorTask/getExamFilterRate`,
    data,
  )
}
// 书题目推荐题已确认题目
export function getBookQuestionRecommendStatusApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/book/question/recommend/confirm`,
    data,
  )
}
// 书题目推荐题确认修改
export function recommendConfirm(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/task/book/question/recommend/confirm`,
    data,
  )
}

// 书题目推荐题搜索
export function getVectorDetail(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/es/es/vector/detail`,
    data,
    { replace: true },
  )
}
// 错题任务-设置考试题目过滤范围
export function setErrorExamQuestionFilterRangeApi(data) {
  return request.post(
    `${VITE_JZT_API}/tutoring/admin/task/examErrorTask/setExamFilterRate`,
    data,
  )
}
/* 大题批改记录 */
export function getBigQuestionCorrectionRecordApi(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/correction/exerciseRecord`,
    data,
    {
      delay: false,
    },
  )
}

// 获取任务模式学生列表
export function getPatternStudentList(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/pattern/student/list`,
    data,
  )
}
// 学生多轮报告列表
export function getExerciseReport(data) {
  return request.get(
    `${VITE_JZT_API}/tutoring/admin/task/schedule/question/retry/exercise/report`,
    data,
  )
}
