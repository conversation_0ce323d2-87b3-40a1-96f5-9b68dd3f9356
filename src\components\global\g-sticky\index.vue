<script lang="ts" setup>
interface Props {
  container?: string
  top?: number
  autoWidth?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  container: '.right-main .n-scrollbar-container',
  top: 0,
  autoWidth: true, // 粘性元素跟随容器宽度变化
})

let stickyRef = $ref<any>(null)
let isSticky = $ref(false)
let stickyStyle = $ref<any>({})
let oldStickyStyle = $ref<any>({})
let containerRef = $ref<any>(null)
let container
let oldStickyOffsetTop
let isRafScheduled = false

const resizeObserver = new ResizeObserver(() => {
  handleResize()
})

onMounted(() => {
  setupPosition()
  container = document.querySelector(props.container)
  container?.addEventListener('scroll', scheduleUpdatePosition)

  resizeObserver.observe(containerRef)
})

// 初始化
async function setupPosition() {
  stickyStyle = {}
  oldStickyStyle = {}
  isSticky = false
  oldStickyOffsetTop = stickyRef.offsetTop
  const {
    width,
    height,
  } = window.getComputedStyle(stickyRef)

  oldStickyStyle = {
    width,
    height,
  }

  scheduleUpdatePosition()
}

function handleResize() {
  oldStickyOffsetTop = stickyRef.offsetTop
  if (props.autoWidth) {
    const { width } = window.getComputedStyle(containerRef)
    oldStickyStyle.width = width
  }
  scheduleUpdatePosition()
}

function scheduleUpdatePosition() {
  if (!isRafScheduled) {
    isRafScheduled = true
    requestAnimationFrame(() => {
      updatePosition()
      isRafScheduled = false
    })
  }
}

function updatePosition() {
  const currentScrollTop = container?.scrollTop
  isSticky = currentScrollTop >= oldStickyOffsetTop - props.top
  let transformTop = props.top - oldStickyOffsetTop

  if (!isSticky)
    transformTop = -currentScrollTop

  stickyStyle = {
    position: 'absolute',
    zIndex: 999,
    width: oldStickyStyle?.width ?? '',
    transform: `translate3D(0, ${transformTop}px, 0)`,
  }
}

function handleWheel(e) {
  const {
    scrollLeft = 0,
    scrollTop = 0,
  } = container
  container.scrollTo({
    left: scrollLeft + e.deltaX,
    top: scrollTop + e.deltaY,
  })
}

onBeforeUnmount(() => {
  container?.removeEventListener('scroll', scheduleUpdatePosition)
  resizeObserver.disconnect()
})
</script>

<template>
  <div ref="containerRef">
    <div
      v-bind="$attrs"
      ref="stickyRef"
      :style="stickyStyle"
      @wheel="handleWheel"
    >
      <slot :is-sticky="isSticky"></slot>
    </div>
    <div
      :style="{
        width: oldStickyStyle.width,
        height: oldStickyStyle.height,
      }"
    ></div>
  </div>
</template>
