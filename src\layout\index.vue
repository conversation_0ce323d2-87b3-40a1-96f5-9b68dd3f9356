<script lang="ts" setup>
import { useRouterStore } from '@/stores/modules/router'
import { useSettingStore } from '@/stores/modules/setting'
import Menu from './components/Menu.vue'

const route = useRoute()
const routerStore = useRouterStore()
const settingStore = useSettingStore()
const { keepAliveArr } = storeToRefs(routerStore) as any
let transitionName = $ref('')

/** 菜单折叠状态 */
let menuCollapsed = $ref(false)

/** 监听路由变化控制菜单折叠状态 */
watch(
  () => route.name,
  async (newName) => {
    menuCollapsed = !['TeacherHome', 'CourseMain'].includes(newName as any)
    if (route.name === 'TeacherHome') {
      transitionName = ''
    }
    else {
      await nextTick()
      transitionName = 'animation'
    }
  },
  { immediate: true },
)

watch(() => menuCollapsed, (newVal) => {
  settingStore.menuCollapsed = newVal
})
// 判断当前路由是否属于不显示菜单的路由列表
const hiddenMenuRoutes = ['teacher/ccyReport', 'debugging', 'teacher/taskCenter/report']

/** 是否显示菜单 */
const showMenu = computed(() => {
  // 检查当前路由是否包含在隐藏菜单的路由中
  return !hiddenMenuRoutes.some(routeSegment => route.path.includes(routeSegment))
})

/** 是否为首页 */
const isHome = computed(() => ['TeacherHome', 'CourseMain'].includes(route.name as any))
</script>

<template>
  <!-- 添加菜单组件和主内容区布局 -->
  <div class="flex h-screen sticky-root">
    <!-- 菜单组件 -->
    <Menu
      v-if="showMenu"
      v-model:collapsed="menuCollapsed"
      class="z-10 h-full transition-all duration-300"
      :class="[
        isHome ? 'absolute' : 'absolute',
        menuCollapsed ? 'w-0' : ($g.isPC ? 'w-180px' : 'w-0'),
      ]"
    />
    <!-- 主内容区 -->
    <div
      class="flex-1 transition-all duration-300"
      :class="{
        'ml-0': !isHome || menuCollapsed || !showMenu,
        'ml-180px': isHome && !menuCollapsed && $g.isPC && showMenu,
      }"
    >
      <router-view v-slot="{ Component }">
        <transition :name="transitionName" mode="out-in">
          <keep-alive :include="keepAliveArr">
            <component :is="Component" :key="$route.name" />
          </keep-alive>
        </transition>
      </router-view>
    </div>
  </div>
</template>

<style>
/* 轻微放大缩小动画配置代码 */
.animation-enter-from,
.animation-leave-to {
  transform: scale(0.95);
  opacity: 0;
}

.animation-enter-to,
.animation-leave-from {
  transform: scale(1);
  opacity: 1;
}

.animation-enter-active {
  transition: all 0.1s ease-out;
}

.animation-leave-active {
  transition: all 0.1s ease-in;
}
</style>
