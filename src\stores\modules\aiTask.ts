export const useAiTaskStore = defineStore('aiTask', {
  state: () => ({
    // 课程编辑状态
    modifyMap: {},
    // 留言状态
    leaveMsg: {},
    // 学生分层数据
    studentLevelData: [] as Array<{
      layerType: number
      layerTypeName: string
      studentList: Array<{
        schoolStudentId: number
        accountId: number
        idNum: string
        studentName: string
        headPicture: string
        studentScore?: string // 新增学生得分字段
      }>
    }>,
  }),
  getters: {
    getLayerStudents: (state) => {
      return state?.studentLevelData?.map((item: any) => {
        return {
          layerType: item?.layerType,
          schoolStudentIdList: item?.studentList.map(it => it?.schoolStudentId),
        }
      })
    },
  },
  actions: {
    // 标记为已编辑
    setModified(id: any) {
      this.modifyMap[id] = true
    },
    // 判断是否已编辑过
    isModified(id: any) {
      return !!this.modifyMap[id]
    },
    // 标记为已编辑
    setLeaveMsg(id: any) {
      this.leaveMsg[id] = true
    },
    // 判断是否已编辑过
    isLeaveMsg(id: any) {
      return !!this.leaveMsg[id]
    },
    // 清除数据
    clearAll() {
      this.modifyMap = {}
      this.leaveMsg = {}
      this.studentLevelData = []
    },
  },
  persist: {
    storage: localStorage,
  },
})
