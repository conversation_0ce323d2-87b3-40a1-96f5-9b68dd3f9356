export const useTaskStore = defineStore('task', {
  state: () => ({
    classList: [] as Array<any>, // 自定义已选择班级列表，selectStudentArr或selectGroupArr任中有一个长度不为0，该班级就被返回。
    specialClass: null as any, // 非自定义班级
    selectStudentList: [] as Array<number | string>, // 所有选择的学生id总和，已去重
    disabledStudentIds: [] as Array<number | string>, // 需要禁止操作的学生id集合
    disabledGroupIds: [] as Array<number | string>, // 需要禁止操作的组id集合

  }),
  actions: {},
})
