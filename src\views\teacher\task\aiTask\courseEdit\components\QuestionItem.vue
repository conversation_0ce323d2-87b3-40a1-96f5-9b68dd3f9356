<script setup lang="ts">
const props = defineProps({
  questionItem: {
    type: Object,
    required: true,
  },
  curPaper: {
    type: Object,
    default: () => ({}),
  },
  questionIndex: {
    type: Number,
    default: 0,
  },
  renderIfChange: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['openChangeQes'])
const route = useRoute()
let hasExpand = $ref(false)
let expand = $ref(false)
let maxHeight = $ref(300)
let observer: any = null
const contentRef = $ref<any>()

// 用在虚拟列表中时，需要监听题目数据变化，重新渲染数学公式
if (props.renderIfChange) {
  watch(() => props.questionItem, () => {
    nextTick($g.tool.renderMathjax)
  }, {
    deep: false,
    immediate: true,
  })
}

let containerRef = $ref<any>(null)
let currentSubIndex = $ref(0)

const LAYERTYPE = {
  1: '高分组',
  2: '中分组',
  3: '低分组',
}
const layerTypeNumber = $computed(() => {
  if (!$g.tool.isTrue(route.query?.layerType)) return false
  return Number(route.query?.layerType)
})

let subQuestion = $computed(() => props.questionItem.subQuestions?.map((item) => {
  return {
    ...item,
    currentSubParseIndex: 0,
    optionArr: Object.keys(item)
      .filter(
        key =>
          key.includes('option') && item[key] && key !== 'optionNumber',
      )
      .map((realKey) => {
        return {
          name: realKey.charAt(realKey.length - 1).toLocaleUpperCase(),
          title: item[realKey].toString(),
        }
      }),
  }
}))

const currentSubItem = $computed(() => {
  return subQuestion[currentSubIndex]
})

function openParse() {
  props.questionItem.showParse = !props.questionItem.showParse
  if (props.questionItem.showParse) {
    containerRef.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
  nextTick($g.tool.renderMathjax)
}

// 换题
function openChangeDialog() {
  emits('openChangeQes', props.questionItem)
}

async function init() {
  observer?.disconnect?.()
  observer = null
  await nextTick()
  $g.tool.renderMathjax()
  observer = new ResizeObserver(() => {
    hasExpand = contentRef?.scrollHeight > maxHeight
    if (hasExpand) {
      observer?.disconnect?.()
      observer = null
    }
  })
  observer.observe(contentRef)
}

watch(() => props.questionItem, (val) => {
  if (val)
    init()
}, {

  immediate: true,
})
</script>

<template>
  <div ref="containerRef" class="h-auto p-16px question-item">
    <div class="flex items-start">
      <div class="flex-1 overflow-hidden">
        <!-- 大题题干 -->
        <div
          ref="contentRef"
          class="overflow-hidden"
          :style="{ maxHeight: expand ? 'unset' : `${maxHeight}px` }"
        >
          <g-mathjax
            v-if="questionItem.questionTitle"
            class="mt-10px"
            :text="questionItem.questionTitle"
            :order="`${questionIndex + 1}. `"
          />
        </div>

        <div
          v-if="hasExpand"
          class="text-[#6474FD] text-15px flex items-center mt-11px cursor-pointer pl-16px"
          @click="expand = !expand"
        >
          <span class="mr-6px">{{ expand ? '收起' : '展开查看更多' }}</span>
          <img
            :src="expand ? $g.tool.getFileUrl('taskCenter/blueTop.png') : $g.tool.getFileUrl('taskCenter/blueBottom.png')"
            alt=""
            class="w-15px h-9px"
          >
        </div>

        <!-- 题号选择 -->
        <div v-if="subQuestion?.length > 1" class="my-10px">
          <span
            v-for="(sub, ti) in subQuestion"
            :key="ti"
            class="inline-block bg-[#ccc]/60 w-30px h-30px text-center leading-[30px] rounded-full mr-10px cursor-pointer"
            :class="{ '!bg-[#6474FD] !text-white': ti == currentSubIndex }"
            @click="currentSubIndex = ti"
          >
            {{ ti + 1 }}
          </span>
        </div>

        <g-mathjax :text="currentSubItem.subQuestionTitle" class="text-16px" />
        <div
          v-for="item in currentSubItem.optionArr"
          :key="item.name"
          :class="questionItem.questionTitle && currentSubItem.subQuestionTitle ? 'pl-32px' : 'pl-16px'"
          class="flex items-center py-10px"
        >
          <span class="flex-shrink-0 mr-4px"> {{ item.name }}. </span>
          <g-mathjax :text="item.title" />
        </div>
      </div>
      <slot name="extraData"></slot>
    </div>

    <!-- 分割线 -->
    <div class="w-full h-[1px] bg-[#D8D8D8] mt-[18px]" />

    <slot name="footer" :question-item="questionItem">
      <div class="flex items-center justify-between mt-10px">
        <slot name="tagList">
          <!-- 标签列表 -->
          <div class="flex items-center flex-wrap">
            <!-- 分组 -->
            <template v-if="layerTypeNumber">
              <el-tag
                class="h-26px mt-4px"
                :class="{
                  'bg-[#FFFAD2] text-[#FFB200] border-[#FFB200]': layerTypeNumber == 1,
                  '!bg-[#E4FFD2] !text-[#18D000] !border-[#18D000]': layerTypeNumber == 2,
                  '!bg-[#D2EFFF] !text-[#009DFF] !border-[#009DFF]': layerTypeNumber == 3,
                }"
              >
                {{ LAYERTYPE[layerTypeNumber] }}
              </el-tag>
              <el-divider direction="vertical" class="mt-2px"></el-divider>
            </template>
            <!-- 年份 -->
            <div v-if="questionItem.year" class="tag">
              {{ questionItem.year }}
            </div>
            <!-- 题型 -->
            <div class="tag">
              {{ questionItem.sysQuestionTypeName || '其他' }}
            </div>
            <!-- 难度 -->
            <div class="tag">
              {{ questionItem.sysQuestionDifficultyName || '其他' }}
            </div>
            <!-- 知识点 -->
            <template v-if="questionItem?.knowledgePoints?.length">
              <div
                v-for="item in questionItem.knowledgePoints"
                :key="item.commonKnowledgePointsId"
                class="tag"
              >
                {{ item.sysKnowledgePointName }}
              </div>
            </template>
          </div>
        </slot>
        <div class="flex items-center">
          <el-button type="primary" @click="openChangeDialog">
            更换试题
          </el-button>
          <el-button
            v-if="!questionItem.showParse"
            type="primary"
            class="flex cursor-pointer items-center"
            @click="openParse"
          >
            答案及解析
            <svg-ri-arrow-down-s-line class="w-[16px] ml-[4px] h-[16px] text-white" />
          </el-button>
          <el-button
            v-else
            class="flex items-center cursor-pointer  bg-[#ECEFFF]"
            @click="openParse"
          >
            <div
              class="text-[#6474FD] text-[13px] p-[6px] w-fit br-[5px] flex items-center"
            >
              收起答案
              <svg-ri-arrow-up-s-line class="w-[16px] ml-[4px] h-[16px] text-[#6474FD]" />
            </div>
          </el-button>
        </div>
      </div>
    </slot>
    <!-- 解析/答案 -->
    <div v-show="questionItem.showParse" class="mt-13px bg-[#F3F4F9] br-[6px] p-16px">
      <div class="flex">
        <span class="text-[13px] mr-[20px] text-[#6C6C74] flex-shrink-0">
          答案
        </span>
        <div class="flex-1">
          <g-mathjax :text="currentSubItem.subQuestionAnswer" class="text-16px" />
        </div>
      </div>
      <div class="flex mt-[13px]">
        <div class="mr-[20px] text-[13px] text-[#6C6C74] flex-shrink-0">
          解析
        </div>
        <div class="flex-1">
          <g-mathjax
            v-for="item in currentSubItem.subQuestionParseList"
            :key="item.subQuestionParseId"
            :text="item.content"
            class="text-16px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter,
.fade-leave-active {
  opacity: 0.3;
}

.tag {
  @apply mr-8px text-[12px] border bg-[#F4F5FA] p-[3px] w-fit br-[5px] mt-4px text-[#6C6C74] border-[#D7DDE9];
}

.question-item {
  :deep() {
    .g-mathjax {
      overflow: hidden;
      * {
        white-space: pre-wrap;
      }

      & > :first-child::before {
        font-size: 15px;
      }
    }
  }
}
</style>
