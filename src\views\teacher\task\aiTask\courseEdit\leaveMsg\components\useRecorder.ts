import type { IMediaRecorder } from 'extendable-media-recorder'
import { MediaRecorder, register } from 'extendable-media-recorder'
import { connect } from 'extendable-media-recorder-wav-encoder'

// 编码器注册状态标志
let encoderRegistered = false
const recordingStartTime = ref<number | null>(null)
// 全局注册编码器函数
async function registerEncoderOnce() {
  if (typeof window !== 'undefined' && !encoderRegistered) {
    try {
      const port = await connect()
      register(port)
      encoderRegistered = true
    }
    catch (error) {
      console.error('Failed to register WAV encoder:', error)
    }
  }
}

export function useRecorder() {
  const mediaRecorder = ref<IMediaRecorder | null>(null)
  const audioChunks = ref<Blob[]>([])
  const isRecording = ref(false)
  const stream = ref<MediaStream | null>(null)

  // 开始录音
  const startRecording = async () => {
    try {
      // 确保编码器已注册
      await registerEncoderOnce()
      stream.value = await navigator.mediaDevices.getUserMedia({ audio: true })

      mediaRecorder.value = new MediaRecorder(stream.value, {
        mimeType: 'audio/wav',
      })

      audioChunks.value = []

      mediaRecorder.value.ondataavailable = (event) => {
        if (event.data.size > 0)
          audioChunks.value.push(event.data)
      }
      recordingStartTime.value = Date.now()
      // 开始录制
      mediaRecorder.value.start()
      isRecording.value = true
    }
    catch (error) {
      console.log(error)
      $g.showToast('录音失败', error)
    }
  }

  const stopAllTracks = () => {
    if (stream.value) {
      stream.value.getTracks().forEach(track => track.stop())
      stream.value = null
    }
  }

  // 停止录音并返回 Blob
  const stopRecording = (): Promise<Blob | null> => {
    return new Promise((resolve) => {
      if (!mediaRecorder.value) {
        resolve(null)
        return
      }
      const recordingDuration = recordingStartTime.value
        ? Date.now() - recordingStartTime.value
        : 0

      mediaRecorder.value.onstop = (event) => {
        stopAllTracks()
        const audioBlob = new Blob(audioChunks.value, {
          type: 'audio/wav',
        })

        if (
          recordingDuration < 1000
        )
          $g.showToast('说话时间太短')
        else
          resolve(audioBlob)
      }

      mediaRecorder.value.stop()
      isRecording.value = false
      recordingStartTime.value = null
    })
  }

  const resetRecording = () => {
    stopAllTracks()
    isRecording.value = false
    audioChunks.value = []
    recordingStartTime.value = null
  }

  // 获取录音blob (仅用于已经存在的 blob)
  const getAudioBlob = (): Blob[] | null => {
    return audioChunks.value
  }

  return {
    isRecording,
    startRecording,
    stopRecording,
    resetRecording,
    getAudioBlob,
  }
}
