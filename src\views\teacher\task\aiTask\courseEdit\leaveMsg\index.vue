<script lang="ts" setup>
import { editLeaveMsgModify, getLeaveMsgList } from '@/api/aiTask'
import { useAiTaskStore } from '@/stores/modules/aiTask'
import RecordDialog from './components/RecordDialog.vue'
import Upload from './components/Upload.vue'

const aiTaskStore = useAiTaskStore()
const route = useRoute()
const router = useRouter()
const form = $ref({
  content: '',
  audio: [] as any,
  video: [] as any,
  image: [],
})

let showDialog = $ref(false)
let currentDialogType = $ref('')
let previewVideoUrl = $ref('')
let isPlaying = $ref(false)
let audioInstance: HTMLAudioElement | null = $ref(null)
let showRecordDialog = $ref(false)
let showAudio = $ref(true)

const disabledBtn = $computed(() => {
  return [
    form.content,
    form.audio,
    form.video,
    form.image,
  ].some(item => $g.tool.isTrue(item))
})
// 获取留言列表
function fetchMsgList() {
  getLeaveMsgList({
    bookCatalogId: route.query?.bookCatalogId,
    taskResourceType: route.query?.taskResourceType,
    taskResourceFromId: route.query?.taskResourceFromId,
  }).then((res) => {
    form.content = res?.find(v => v.leaveWordType == 1)?.leaveWordContent || ''
    const audio = res?.filter(v => v.leaveWordType == 2)?.map((v) => {
      return {
        resource_url: v.leaveWordContent,
        duration: v.resourceDuration,
      }
    })
    form.audio = audio
    const video = res?.filter(v => v.leaveWordType == 3)?.map((v) => {
      return {
        resource_url: v.leaveWordContent,
        url: `${v.leaveWordContent}?x-oss-process=video/snapshot,t_1000,f_jpg,w_400,h_600,m_fast`,
        duration: v.resourceDuration,
      }
    })
    form.video = video
    const image = res?.filter(v => v.leaveWordType == 4)?.[0]
    const handleImg = image?.leaveWordContent?.split([','])
    form.image = handleImg?.map((v) => {
      return {
        resource_url: v,
        url: v,
      }
    }) || []
  })
}

// 资源预览
function handlePictureCardPreview(uploadFile, type) {
  const fileList = form[type]
  const index = fileList.findIndex(item => item.uid == uploadFile.uid)
  const list = fileList.map(item => item.fullUrl)
  if (type == 'image') {
    $g.flutter('previewImage', {
      urls: list,
      index,
      canShare: false,
      isShowDown: false,
      onTapClose: true,
    })
  }
  else {
    !$g.inApp
      ? handleVideo(fileList[index]?.resource_url, type)
      : $g.flutter('playVideo', {
          title: '',
          resource_url: fileList[index]?.resource_url,
        })
  }
}

function handleVideo(url, type) {
  currentDialogType = type
  showDialog = true
  previewVideoUrl = url
}

function playAudio(url: string) {
  if (audioInstance) {
    audioInstance.pause()
    audioInstance = null
  }
  audioInstance = new Audio(url)
  audioInstance.play()
  isPlaying = true
  audioInstance.onended = () => {
    isPlaying = false
  }
}

function pauseAudio() {
  if (audioInstance) {
    audioInstance.pause()
    isPlaying = false
  }
}

function delAudio() {
  form.audio = []
  audioInstance = null
}

const submitThro = $g._.throttle(() => {
  submit()
}, 1000, {
  leading: true,
  trailing: false,
})

function submit() {
  const formData = $g._.cloneDeep(form)
  let leaveWordList: any = []
  // 文字留言
  if (formData.content) {
    leaveWordList.push({
      leaveWordType: 1,
      leaveWordContent: formData.content,
    })
  }
  // 语音留言
  if (Array.isArray(formData.audio) && formData.audio.length) {
    formData.audio.forEach((item) => {
      leaveWordList.push({
        leaveWordType: 2,
        leaveWordContent: item.resource_url,
        resourceDuration: item.duration,
      })
    })
  }
  // 视频留言
  if (Array.isArray(formData.video) && formData.video.length) {
    formData.video.forEach((item: any) => {
      leaveWordList.push({
        leaveWordType: 3,
        leaveWordContent: item.resource_url,
        resourceDuration: item.duration,
      })
    })
  }
  // 图片留言
  if (Array.isArray(formData.image) && formData.image.length) {
    leaveWordList.push({
      leaveWordType: 4,
      leaveWordContent: formData.image?.map((v: any) => v.resource_url)?.join(','),
    })
  }
  const params = {
    bookCatalogId: route.query?.bookCatalogId,
    taskResourceType: route.query?.taskResourceType,
    taskResourceFromId: route.query?.taskResourceFromId,
    leaveWordList,
  }
  editLeaveMsgModify(params).then((res) => {
    $g.showToast('保存成功')
    router.back()
    if (aiTaskStore.isLeaveMsg(route.query?.taskResourceFromId))
      return

    aiTaskStore.setLeaveMsg(route.query?.taskResourceFromId)

    const bookCatalogId = route.query?.bookCatalogId
    if (!bookCatalogId) return
    if (aiTaskStore.isModified(bookCatalogId))
      return

    aiTaskStore.setModified(bookCatalogId)
  })
}

function receiveMsg(msg) {
  form.audio = [{
    resource_url: msg?.url,
    duration: msg?.duration,
  }]
}

// 时间格式优化
function formatSeconds(totalSeconds: number): string {
  const hours = Math.floor(totalSeconds / 3600)
  const remainingSeconds = totalSeconds % 3600
  const minutes = Math.floor(remainingSeconds / 60)
  const seconds = remainingSeconds % 60

  const parts: any = []
  if (hours > 0) parts.push(`${hours}'`)
  if (minutes > 0 || hours > 0) parts.push(`${minutes}'`) // 如果小时存在，分钟即使0也显示
  parts.push(`${seconds}`)

  return parts.join('')
}

watch(() => form.audio, (val) => {
  if (val.length) {
    showAudio = false
  }
  else {
    setTimeout(() => {
      showAudio = true
    }, 200)
  }
}, { deep: true })

onBeforeMount(() => {
  audioInstance = null
  fetchMsgList()
})
</script>

<template>
  <div class="h-screen p-26px !pb-0 flex flex-col leave-msg" style="width: 100vw;">
    <g-navbar title="课程留言" class="mb-15px">
    </g-navbar>
    <div class="bg-white p-17px rounded-[6px] flex-1">
      <el-form :model="form">
        <el-form-item label="文字留言">
          <el-input
            v-model.trim="form.content"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 10 }"
            placeholder="请输入内容"
            maxlength="500"
            show-word-limit
            class="w-[50%] msg-input"
          ></el-input>
        </el-form-item>
        <el-form-item label="语音留言">
          <div v-show="!form.audio.length && showAudio" class="flex">
            <Upload
              v-model:file-list="form.audio"
              mode="picture-card"
              accept=".mp3,.wav"
              tips="只支持上传1个音频"
              :limit="1"
              :class="{ 'hide-upload-btn': form.audio.length >= 1 }"
              :on-preview="(event) => handlePictureCardPreview(event, 'audio')"
            >
            </Upload>
            <svg-ri-mic-line class="text-20px text-[#6474FD] cursor-pointer mt-35px" @click="showRecordDialog = true" />
          </div>
          <Transition name="van-fade">
            <template v-if="form.audio.length">
              <div class="bg-white w-[40%] h-41px flex items-center justify-between rounded-[11px] pr-10px border border-[#DCDFE6]">
                <div class="w-41px h-41px rounded-[11px_0_0_11px] bg-[#EBEDFF] flex items-center justify-center cursor-pointer" @click="isPlaying ? pauseAudio() : playAudio(form.audio[0]?.resource_url)">
                  <div class="w-17px h-17px border border-[#6474FD] rounded-full text-[#6474FD] flex justify-center items-center">
                    <svg-ri-play-line v-if="!isPlaying" />
                    <svg-ri-pause-mini-line v-else />
                  </div>
                </div>
                <span class="pl-20px flex-1 text-[#666] text-12px">音频时长：{{ formatSeconds(Math.round(form.audio[0]?.duration || 0)) }}</span>
                <svg-ri-delete-bin-line class="text-[#6474FD] cursor-pointer w-17px h-17px" @click="delAudio" />
              </div>
            </template>
          </Transition>
        </el-form-item>

        <el-form-item label="视频留言" class="video-upload">
          <Upload
            v-model:file-list="form.video"
            mode="picture-card"
            accept=".mp4"
            tips="只支持上传1个视频，大小不超过100M"
            :limit="1"
            :max-size="1024 * 1024 * 100"
            :class="{ 'hide-upload-btn': form.video.length >= 1 }"
            :on-preview="(event) => handlePictureCardPreview(event, 'video')"
          >
          </Upload>
        </el-form-item>
        <el-form-item label="图片留言">
          <Upload
            v-model:file-list="form.image"
            mode="picture-card"
            accept="image/*"
            tips="只支持上传5张图片，单张大小不超过20M"
            :limit="5"
            multiple
            :max-size="1024 * 1024 * 20"
            :class="{ 'hide-upload-btn': form.image.length >= 5 }"
            :on-preview="(event) => handlePictureCardPreview(event, 'image')"
          >
          </Upload>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex justify-end items-center bg-white mt-26px h-58px -mx-26px pr-26px">
      <el-button @click="router.back()">
        取消
      </el-button>
      <el-button
        type="primary"
        :disabled="!disabledBtn"
        @click="submitThro"
      >
        完成
      </el-button>
    </div>

    <el-dialog v-model="showDialog">
      <div class="pt-10px">
        <g-video
          v-if="currentDialogType == 'video'"
          :url="previewVideoUrl"
          class="w-full h-[400px]"
        />
      </div>
    </el-dialog>
    <RecordDialog v-model:show="showRecordDialog" @receive-msg="receiveMsg" />
  </div>
</template>

<style scoped lang="scss">
.hide-upload-btn :deep(.el-upload--picture-card) {
  display: none;
}
.video-upload {
  :deep() {
    .el-icon--close-tip {
      display: none!important;
    }
  }
}
.msg-input {
  :deep(.el-textarea__inner) {
    background: #FAFBFF;
  }
}
.leave-msg {
  :deep() {
    .el-dialog {
      border-radius: 12px !important;
      background:linear-gradient(#E5E8FF 0%, #fff 100%);
    }
  }
}
</style>
