<script setup lang="ts">
/**
 * 全屏状态下的倒计时组件
 */

interface Props {
  /** 倒计时时间（毫秒） */
  time: number
  /** 时间剩余百分比 */
  timePercentage: number
  /** 任务是否已完成 */
  isTaskCompleted: boolean
  /** 任务是否已开始 */
  isTaskStarted: boolean
  /** 倒计时颜色类配置 */
  countdownColorClass: {
    colorClass?: string
    iconSrc?: string
  }
  /** 是否自定义样式 */
  customClass?: string
}

interface Emits {
  (e: 'change', timeData: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
</script>

<template>
  <div :class="customClass ? customClass : 'absolute top-12px left-1/2 transform -translate-x-1/2 z-10'">
    <div
      class="flex items-center px-17px py-6px rounded-[20px] text-14px font-500 text-[#333]"
      :class="[
        isTaskCompleted
          ? 'bg-[#F5F5F5] border border-[#D9D9D9]'
          : timePercentage >= 50
            ? 'bg-[#F4FFF4] border border-[#5BE361]'
            : timePercentage > 10
              ? 'bg-[#FFFBE6] border border-[#FFE58F]'
              : 'bg-[#FFF0F0] border border-[#F92A2A]',
      ]"
    >
      <svg-ri-time-line
        v-if="!isTaskCompleted"
        class="text-18px mr-6px"
        :class="[
          countdownColorClass.colorClass,
        ]"
      />
      <template v-if="isTaskCompleted">
        课程已结束
      </template>
      <template v-else-if="!isTaskStarted">
        课程将于
        <van-count-down
          :time="time"
          format="mm:ss"
          class="mx-4px"
        />
        后开始
      </template>
      <template v-else>
        课程将于
        <van-count-down
          :time="time"
          format="mm:ss"
          class="mx-4px"
        />
        后结束
      </template>
    </div>
  </div>
</template>
