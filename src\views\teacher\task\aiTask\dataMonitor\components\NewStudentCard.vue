<script setup lang="ts">
/** 学生信息接口定义 */
interface IStudentInfo {
  /** 学生ID */
  schoolStudentId: number
  /** 学生姓名 */
  schoolStudentName: string
  /** 头像地址 */
  headPicture?: string
  /** 举手时间 */
  createTime: string
  /** 举手时学习的时间（秒） */
  raiseHandDuration: number
  /** 位置信息 */
  catalogCourseTypeName?: string
  /** 目录名称 */
  bookCatalogName?: string
  /** 任务ID */
  taskId?: number
  /** 书目录ID */
  bookCatalogId?: number
  /** 章节标签 */
  catalogCourseType?: number
  /** 举手类型:1-AI,2-老师 */
  raiseHandType?: number
  /** 举手状态:1-举手中,2-解答中,3-已解答 */
  raiseHandState?: number
  /** 举手记录ID */
  taskStudentRaiseHandId?: number
}

/** 组件属性定义 */
interface IProps {
  /** 学生信息 */
  studentInfo: IStudentInfo
  /** 是否可点击解答按钮 */
  canAnswer?: boolean
  /** 是否已解答 */
  answered?: boolean
  /** 是否为AI模式 */
  isAIMode?: boolean
  /** 按钮loading状态 */
  loading?: boolean
}

/** 学生卡片属性 */
const props = withDefaults(defineProps<IProps>(), {
  studentInfo: () => ({ }) as IStudentInfo,
  canAnswer: true,
  answered: false,
  isAIMode: false,
  loading: false,
})

/** 定义事件 */
const emit = defineEmits<{
  /** 点击解答按钮事件 */
  (e: 'answer', studentInfo: IStudentInfo): void
}>()

/**
 * 将秒数转换为"分'秒''"格式
 * @param seconds 秒数
 * @returns 格式化后的时间字符串
 */
function formatDuration(seconds: number): string {
  if (!seconds && seconds !== 0) return '--'

  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)

  return `${minutes}'${remainingSeconds.toString().padStart(2, '0')}''`
}

/** 处理解答点击 */
function handleAnswer() {
  emit('answer', props.studentInfo)
}
</script>

<template>
  <div class="student-card p-[8px] flex items-center rounded-[4px]" :class="{ 'bg-[#F3F4F9]': !answered }">
    <van-image
      :src="studentInfo.headPicture"
      :round="true"
      class="w-38px h-38px mr-5px"
      style="border: 1px solid #FFFFFF; "
    ></van-image>
    <div class="flex-1">
      <div class="text-15px font-500">
        {{ studentInfo.schoolStudentName }}
      </div>
      <div v-if="!answered" class="text-12px text-[#666] flex items-center">
        <span>{{ $g.dayjs(studentInfo.createTime).format('HH:mm:ss') }}</span>
        <div class="fgx"></div>
        <span>{{ studentInfo.bookCatalogName || '无' }}</span>
        <span class="ml-5px">{{ formatDuration(studentInfo.raiseHandDuration) }}</span>
      </div>
    </div>
    <van-button
      v-if="!isAIMode"
      type="primary"
      class="w-53px h-30px p-0"
      :class="{ 'answered-btn': answered }"
      :loading="loading"
      :disabled="loading || answered"
      @click="handleAnswer"
    >
      {{ answered ? '已解答' : '解答' }}
    </van-button>
  </div>
</template>

<style lang="scss" scoped>
.student-card {}

.fgx {
  display: inline-block;
  width: 1px;
  height: 12px;
  margin: 0 5px;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  text-align: left;
  background-color: #999;
  font-style: normal;
}

:deep(){
  .answered-btn {
  background-color: #E8E8E8 !important;
  border-color: #E8E8E8 !important;
  .van-button__text{
    color: #666666 !important;
  }
}
}
</style>
