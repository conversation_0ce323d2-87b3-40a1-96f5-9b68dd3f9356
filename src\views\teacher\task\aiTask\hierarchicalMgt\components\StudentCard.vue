<script setup lang="ts">
const props = defineProps<{
  student: {
    schoolStudentId: number
    accountId: number
    idNum: string
    studentName: string
    headPicture: string
    studentScore?: string // 学生成绩字段
  }
  /** 是否显示拖拽图标，默认显示 */
  showDragIcon?: boolean
}>()

// 设置默认值
const { showDragIcon = true } = props
</script>

<template>
  <div class="h-[60px]  border  border-[#DCDFE6] rounded-[11px] flex justify-between items-center pr-14px mb-12px cursor-pointer">
    <div class="flex">
      <img
        class="w-58px h-58px rounded-[11px] mr-10px"
        :src="student.headPicture"
      />
      <div class="flex flex-col justify-center">
        <div class="mb-3px text-13px">
          {{ student.studentName }}
        </div>
        <div v-if="student.studentScore" class="text-11px">
          <span class="mr-6px">本次成绩：{{ student.studentScore }}分</span>
        </div>
      </div>
    </div>
    <div v-if="showDragIcon">
      <svg-ri-menu-line class="text-16px text-theme-primary cursor-pointer"></svg-ri-menu-line>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
