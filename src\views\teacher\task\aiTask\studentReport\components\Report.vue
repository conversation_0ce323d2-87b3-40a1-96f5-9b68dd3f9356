<script setup lang="ts">
import { getStatistics, getStudentLearnList, getTableStatistics } from '@/api/aiTask'
import DataPresentation from '@/views/teacher/task/comprehensiveTask/components/DataPresentation.vue'

const props = defineProps({
  isShowOverviewTable: {
    type: Boolean,
    default: true,
  },
  currentCategoryId: {
    type: Number,
    default: 0,
  },
  catalogCourseType: {
    type: String,
    default: '',
  },
})
const route = useRoute()
let courseOverview: any = $ref({
  items: [
    {
      title: '完成人数',
      slotName: 'finishCount',
      titleClass: '!text-12px',
      dataClass: !$g.isPC ? '!text-17px' : '!text-22px',
      unitClass: !$g.isPC ? '!text-17px !font-600' : '!text-22px !font-600',
    },
    {
      title: '平均进度',
      slotName: 'avgProgressRate',
      unit: '%',
      titleClass: '!text-12px',
      dataClass: !$g.isPC ? '!text-17px' : '!text-22px',
      unitClass: !$g.isPC ? '!text-17px !font-600' : '!text-22px !font-600',
    },
    {
      title: '平均答题数',
      slotName: 'avgAnswerCount',
      titleClass: '!text-12px',
      dataClass: !$g.isPC ? '!text-17px' : '!text-22px',
      unitClass: !$g.isPC ? '!text-17px !font-600' : '!text-22px !font-600',
    },
    {
      title: '平均正确率',
      slotName: 'avgRightRate',
      unit: '%',
      titleClass: '!text-12px',
      dataClass: !$g.isPC ? '!text-17px' : '!text-22px',
      unitClass: !$g.isPC ? '!text-17px !font-600' : '!text-22px !font-600',
    },
    {
      title: '平均学习用时',
      slotName: 'timeList',
      titleClass: '!text-12px',
      dataClass: !$g.isPC ? '!text-17px' : '!text-22px',
      unitClass: !$g.isPC ? '!text-17px !font-600' : '!text-22px !font-600',
    },
  ],
  data: {},
})

const overviewTableOptions = reactive<any>({
  ref: null as any,
  key: '',
  loading: true,
  column: [
    {
      prop: 'bookCatalogName',
      label: '课程名称',
    },
    {
      prop: 'finishCount',
      label: '完成人数',
      slot: true,
    },
    {
      prop: 'learnProgressRate',
      label: '学习进度',
    },
    {
      prop: 'answerCount',
      label: '答题数',
      slot: true,
    },
    {
      prop: 'rightRate',
      label: '正确率',
    },
    // { prop: 'cz', label: '操作', slot: true },
  ],
  data: [],
})

const studentTableOptions = reactive<any>({
  ref: null as any,
  key: '',
  loading: true,
  column: [
    {
      prop: 'schoolStudentName',
      label: '学生姓名',
    },
    {
      prop: 'learnProgressRate',
      label: '学习进度',
    },
    {
      prop: 'answerCount',
      label: '答题数',
      slot: true,
    },
    {
      prop: 'rightRate',
      label: '正确率',
    },
    {
      prop: 'learnDuration',
      label: '学习用时',
    },
    {
      prop: 'finishTime',
      label: '完成时间',
      formatter(row) {
        if (!row?.finishTime)
          return '/'

        return $g.dayjs(row?.finishTime).format('YYYY/MM/DD HH:mm:ss')
      },
    },
    // { prop: 'cz', label: '任务详情', slot: true },
  ],
  data: [],
})

// 秒钟转换时分秒
function secondsToMinutesSeconds(seconds) {
  if (!seconds) return ''
  let hour: any = Math.floor(seconds / 3600)
  let min: any = Math.floor((seconds % 3600) / 60)
  let sec: any = seconds % 60
  if (hour < 10 && hour) hour = `0${hour}`
  if (min < 10 && min) min = `0${min}`
  if (sec < 10 && sec) sec = `0${sec}`
  return [hour,
min,
sec]
}

// 获取课程概览数据
async function getCourseOverview() {
  try {
    const res = await getStatistics({
      taskId: route.query.taskId,
      bookCatalogId: props.currentCategoryId || null,
      catalogCourseType: props.catalogCourseType,
    })
    if (!res) return
    res.timeList = secondsToMinutesSeconds(res.avgLearnDuration)
    courseOverview.data = res
  }
  catch (error) {
    console.log(error)
  }
}

function courseTableDataInit(data) {
  return data.map((v) => {
    v.learnProgressRate = `${v.learnProgressRate}%`
    v.rightRate = `${v.rightRate}%`
    return v
  })
}

// 获取概览表格数据
async function getOverviewTableData() {
  try {
    const res = await getTableStatistics({ taskId: route.query.taskId })
    overviewTableOptions.data = courseTableDataInit(res)
  }
  catch (error) {
    console.log(error)
  }
  finally {
    overviewTableOptions.loading = false
  }
}

// 获取学生列表数据
async function getStudentTableData() {
  try {
    studentTableOptions.loading = true
    const res = await getStudentLearnList({
      taskId: route.query.taskId,
      bookCatalogId: props.currentCategoryId || null,
      catalogCourseType: props.catalogCourseType,
    })
    studentTableOptions.data = res.map((v) => {
      v.learnProgressRate = `${v.learnProgressRate}%`
      v.rightRate = `${v.rightRate}%`
      return v
    })
  }
  catch (error) {
    console.log(error)
  }
  finally {
    studentTableOptions.loading = false
  }
}

// function handlePreview(row: any) {
//   console.log(row)
// }

// function handleDetail(row: any) {
//   console.log(row)
// }

function init() {
  if (props.currentCategoryId === null) return
  getOverviewTableData()
  getCourseOverview()
  getStudentTableData()
}

onMounted(() => {
  init()
})

watch(() => props.currentCategoryId, () => {
  if (props.isShowOverviewTable) getOverviewTableData()
  getCourseOverview()
  getStudentTableData()
})

onActivated(() => {
  init()
})
</script>

<template>
  <div class="h-full w-full overflow-auto no-bar">
    <div class="bg-[white] p-17px br-[6px]">
      <div class="font-600">
        课程概况
      </div>
      <DataPresentation :data-option="courseOverview" class="pt-17px pb-7px">
        <template #finishCount="{ value, data, item }">
          <span class="text-17px font-600" :class="[item.dataClass]">{{ `${value || 0}/${data.totalCount || 0}` }}</span>
        </template>
        <template #timeList="{ value, data, item }">
          <div
            v-if="data.timeList"
            class="text-17px font-600"
            :class="[item.dataClass]"
          >
            <span v-if="value?.[0]">{{ value?.[0] }}时</span>
            <span v-if="value?.[1]">{{ value?.[1] }}分</span>
            <span v-if="value?.[2]">{{ value?.[2] }}秒</span>
          </div>
          <span v-else>/</span>
        </template>
      </DataPresentation>
    </div>
    <div v-show="isShowOverviewTable" class="mt-26px bg-[white] p-17px br-[6px]">
      <span class="font-600 mb-7px">课程进度总览</span>
      <g-table
        stripe
        :border="false"
        :header-cell-style="{
          background: '#6474FD1A',
          color: '#6C6C74',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :cell-style="{
          color: '#333333',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :highlight-current-row="false"
        row-key="bookCatalogId"
        :table-options="overviewTableOptions"
      >
        <template #finishCount="{ row }">
          <span>{{ `${row.finishCount}/${row.totalCount}` }}</span>
        </template>
        <template #answerCount="{ row }">
          <span>{{ `${row.answerCount}/${row.totalAnswerCount}` }}</span>
        </template>
        <!-- <template #cz="{ row }">
          <el-button text type="primary" @click="handlePreview(row)">
            预览
          </el-button>
        </template> -->
      </g-table>
    </div>
    <div class="mt-26px p-17px bg-[white] br-[6px]">
      <span class="font-600 mb-7px">学生列表</span>
      <g-table
        stripe
        :border="false"
        :header-cell-style="{
          background: '#6474FD1A',
          color: '#6C6C74',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :cell-style="{
          color: '#333333',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :highlight-current-row="false"
        :table-options="studentTableOptions"
      >
        <template #answerCount="{ row }">
          <span>{{ `${row.answerCount}/${row.totalAnswerCount}` }}</span>
        </template>
        <!-- <template #cz="{ row }">
          <el-button text type="primary" @click="handleDetail(row)">
            查看
          </el-button>
        </template> -->
      </g-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(){
  .dataTop{
    margin-bottom: 4px !important;
  }
}
</style>
