<script setup lang="ts" name="ComprehensiveTaskPackage">
import {
  createTaskGroup,
  deleteTask,
  editTaskGroup,
  getTaskGroupDetail,
  openTaskPattern,
  taskGroupSubList,
  taskGroupTaskSort,
} from '@/api/comprehensiveTask'
import UpdateTask from '@/views/teacher/home/<USER>/UpdateTask.vue'
import Draggable from 'vuedraggable'

const router = useRouter()
const route = useRoute()
let loading = $ref(true)
let subjectList = $ref<any>([])
let taskScheduleGroupId = $ref(null)

let taskData = ref({
  groupName: '',
  releaseTime: $g.dayjs().format('YYYY-MM-DD HH:mm'),
  courseNum: '',
  estimateTime: '' as any,
  requireCompleteTime: '' as any,
  notIsRecommend: false,
  isImmediate: true,
  isUnlimited: false,
  taskPatternType: '',
})
let patternType = $ref<any>([])
let questionList = ref<any>([])
let resources = ref<any>([])
let updatePicker = $ref<any>('')
let requireCompleteShortcuts = $ref<any>([])
let editTaskId = $ref('')
let showDialog = $ref(false)
let taskDetail = $ref<any>({})
let isChangePatternType = $ref(false) // 手动改变模式后需要先保存才能新增科目相关资源

const releaseTimeShortcuts = [
  {
    text: '立即发布',
    value: () => $g.dayjs().format('YYYY-MM-DD HH:mm'),
  },
]

const PAGE_TYEP = {
  1: '学科网选题',
  2: '校本练习题',
  3: '资源任务',
}

const createFlag = $computed(() => {
  return Boolean(taskData.value.groupName && taskData.value.releaseTime && taskData.value.requireCompleteTime)
})

const recommendedTime = $computed(() => {
  // 如果为试题类型任务
  let minutes = 0
  if ((route.query.pageType as any) != 3) {
    const [objectArr, subjectiveArr] = questionList.value
      .flatMap(item => item.subQuestions)
      .reduce(
        (res, item) => {
          res[[1,2,3].includes(item.subQuestionType)
            ? 0
            : 1].push(item)
          return res
        },
        [[], []],
      )
    minutes = objectArr.length * 2 + subjectiveArr.length * 5
  }
  else {
    minutes = Math.ceil(
      resources.value.reduce(
        (res, item) => res + (item.fileDuration ?? 0),
        0,
      ) / 60,
    )
  }

  let n
  if (!minutes) { // 如果全为文件类资源 默认n为4
    n = 4
  }
  else {
    n = Math.ceil(minutes / 45) // 课时数
  }
  let m = Math.round(n / 2)
  taskData.value.courseNum = n
  taskData.value.estimateTime = minutes || n * 45
  return $g
    .dayjs(taskData.value.releaseTime)
    .endOf('day')
    .add(m - 1, 'day')
    .format('YYYY-MM-DD HH:mm')
})

const releaseTimeFormat = $computed(() => {
  if ($g.dayjs(taskData.value.releaseTime).isSame($g.dayjs().format('YYYY-MM-DD HH:mm'), 'minute')) {
    taskData.value.isImmediate = true
    return '立即发布'
  }
  taskData.value.isImmediate = false
  return 'YYYY-MM-DD HH:mm'
})

// 禁用当前时间之前的日期和时间
function disabledDate(time) {
  const now = $g.dayjs().startOf('day') // 获取今天的00:00:00
  const selectedTime = $g.dayjs(time) // 被选择的时间

  // 禁用今天以前的日期
  return selectedTime.isBefore(now, 'day')
}

const requireCompleteTimeFormat = $computed(() => {
  if (taskData.value.isUnlimited)
    return '不限时'

  if (
    $g
      .dayjs(taskData.value.requireCompleteTime)
      .isSame(recommendedTime, 'minute')
  ) {
    return 'YYYY-MM-DD HH:mm(推荐)'
}

  return 'YYYY-MM-DD HH:mm'
})

const creatFlag = $computed(() => {
  return taskData.value.groupName && taskData.value.releaseTime && taskData.value.requireCompleteTime && taskScheduleGroupId && taskData.value.taskPatternType && !isChangePatternType
})

// 判断科目里面有不有已创建的资源任务
const hasResourceTask = $computed(() => {
  return subjectList.some(item => item.taskList?.length)
})

onMounted(() => {
  $g.bus.on('updateSubList', () => {
    fetchTaskSubListApi()
  })
  taskScheduleGroupId = route.query.taskScheduleGroupId as any
  fetchPatternApi()
  if (!taskScheduleGroupId) {
    loading = false
    return
  }
  fetchTaskGroupDetailApi()
  fetchTaskSubListApi()
})

function handleRecommend(val) {
  taskData.value.notIsRecommend = !$g.dayjs(val).isSame(recommendedTime, 'minute')
  taskData.value.isUnlimited = !taskData.value.requireCompleteTime
}

/**
 * 新增和编辑
 */
function handleAddSubject(item, son?) {
  if (son?.taskId) {
    editTaskId = son.taskId
    showDialog = true
    return
  }
  router.push({
    name: 'CreateTask',
    query: {
      ...route.query,
      taskSource: 'zhrw',
      pattenType: taskData.value.taskPatternType,
      subjectId: item.sysSubjectId,
      subjectName: item.sysSubjectName,
    },
  })
}

/**
 * 拖拽结束
 */
function handleEnd(event, item) {
  const taskIdList = item.taskList?.map(item => item.taskId)
  const params = {
    taskScheduleGroupId,
    sysSubjectId: item.sysSubjectId,
    taskIdList,
  }
  taskGroupTaskSort(params)
}

/**
 * 获取任务模式
 */
function fetchPatternApi() {
  openTaskPattern({ taskType: route.query.pageType }).then((res) => {
    patternType = res || []
    // if (Number(route?.query?.pageType) == 3) {
    //   patternType = patternType.filter(item => item.id != 1)
    // }
    taskData.value.taskPatternType = patternType.length ? (taskDetail?.taskPatternType || patternType?.[0]?.id) : ''
  }).catch((e) => {
      patternType = []
    })
}

/**
 * 获取任务组详情
 */
async function fetchTaskGroupDetailApi() {
  try {
    const {
      groupName,
      releaseTime,
      requireCompleteTime,
      taskPatternType,
    } = await getTaskGroupDetail({ taskScheduleGroupId })
    taskData.value.groupName = groupName
    taskData.value.releaseTime = releaseTime
    taskData.value.isUnlimited = !requireCompleteTime
    taskData.value.requireCompleteTime = taskData.value.isUnlimited ? releaseTime : requireCompleteTime
    taskData.value.taskPatternType = taskPatternType

    taskDetail = {
      groupName,
      releaseTime,
      requireCompleteTime,
      taskPatternType,
    }
  }
  catch (error) {
    loading = false
  }
}

/**
 * 保存子任务名称、时间和类型,保存后就改为修改
 */
function subSonNameTime() {
  if (!createFlag) return
  const {
    groupName,
    taskPatternType,
  } = taskData.value
  const params = {
    taskScheduleId: route.query.taskScheduleId,
    taskType: route.query.pageType,
    groupName,
    taskPatternType,
    taskScheduleGroupId,
  }

  const URL = taskScheduleGroupId ? editTaskGroup : createTaskGroup
  URL(params).then(async (res) => {
    if (res) {
      taskScheduleGroupId = res
      // 获取科目列表
      await fetchTaskSubListApi()
    }
    isChangePatternType = false
    router.replace({
      query: {
        ...route.query,
        taskScheduleGroupId,
      },
    })
  })
}

/**
 * 获取科目列表
 */
async function fetchTaskSubListApi() {
  try {
    const res = await taskGroupSubList({ taskScheduleGroupId })
    res.forEach((item) => {
      // 处理后端返回的null
      item.taskList = item.taskList ?? []
    })
    subjectList = res || []
    loading = false
  }
  catch (error) {
    loading = false
  }
}
// 删除任务（单个）
function deleteTaskApi(item) {
  $g.confirm({
    content: '确定删除任务吗？',
  }).then(async () => {
    try {
      await deleteTask({ taskId: item?.taskId ?? '' })
      $g.msg('删除任务成功')
      fetchTaskSubListApi()
    }
    catch (error) {
      console.log('error => ', error)
    }
  })
}

function updateList() {
  fetchTaskGroupDetailApi()
  fetchTaskSubListApi()
}

watch(
  () => recommendedTime,
  () => {
    requireCompleteShortcuts = [
      {
        text: `推荐时间${recommendedTime}`,
        value: () => {
          taskData.value.isUnlimited = false
          return recommendedTime
        },
      },
      {
        text: '不限时',
        value: () => {
          taskData.value.isUnlimited = true
        },
      },
    ]
    updatePicker = new Date()
    if (taskData.value.notIsRecommend) return
    taskData.value.requireCompleteTime = recommendedTime
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <div class="p-26px !pb-0 flex flex-col h-full">
    <g-navbar :title="`${taskScheduleGroupId ? '编辑' : '创建'}${PAGE_TYEP[route.query.pageType as any]}`" class="pb-21px">
    </g-navbar>

    <div class="flex-1 overflow-y-auto no-bar ">
      <el-scrollbar>
        <div class="rounded-[9px] bg-white p-17px mb-17px">
          <div class="mb-17px">
            <span class="font-600">基础信息</span>
          </div>
          <div class="pb-17px">
            <div class="flex items-center  w-[50%]">
              <span class="pr-16px">任务名称</span>
              <el-input
                v-model.trim="taskData.groupName"
                show-word-limit
                clearable
                maxlength="15"
                placeholder="输入任务名称"
                class="flex-1"
                @blur="subSonNameTime"
              />
            </div>
          </div>

          <template v-if="patternType?.length">
            <!-- <div class="mb-17px font-600">
              子任务模式设置
            </div> -->
            <div class="flex items-center">
              <span class="pr-16px">任务模式</span>

              <el-radio-group v-model="taskData.taskPatternType">
                <el-radio
                  v-for="item in patternType"
                  :key="item.title"
                  :value="item.id"
                  :disabled="hasResourceTask"
                  @change="isChangePatternType = true"
                >
                  <span class="text-15px">{{ item.title }}</span>
                </el-radio>
              </el-radio-group>
            </div>
          </template>
          <!-- <div class="text-center">
            <el-button
              class="text-15px px-22px h-30px mt-17px "
              type="primary"
              :disabled="!createFlag"
              @click="subSonNameTime"
            >
              保存
            </el-button>
          </div> -->
        </div>

        <div class="bg-white p-17px rounded-[9px_9px_0_0] min-h-[calc(100vh-52px-350px)]">
          <div class="font-600 mb-19px">
            科目默写配置
          </div>

          <g-loading v-if="loading" class="h-200px"></g-loading>

          <template v-if="subjectList?.length && !loading">
            <div
              v-for="(item) in subjectList"
              :key="item.sysSubjectId"
              class="mb-17px"
            >
              <div class="flex" :class="{ 'items-center': !item.taskList?.length }">
                <span class="pr-17px">{{ item.sysSubjectName }}</span>

                <div class="flex-1">
                  <Draggable
                    v-model:list="item.taskList"
                    ghost-class="opacity-25"
                    item-key="id"
                    handle=".handle"
                    :animation="150"
                    @end="(e) => handleEnd(e, item)"
                  >
                    <template #item="{ element }">
                      <div class="flex justify-between border border-[#E1E1E1] rounded-[11px] p-[11px_17px_11px_13px] mb-17px">
                        <div>
                          <div class="flex">
                            <svg-task-taskContent class="w-19px h-19px"></svg-task-taskContent>
                            <span class="pl-9px font-500">内容覆盖：{{ element.taskName }}</span>
                          </div>
                          <div class="flex  mt-17px">
                            <svg-task-taskRange_c class="w-19px h-19px"></svg-task-taskRange_c>
                            <span class="pl-9px font-500 flex-1">任务范围：{{ element.taskClassList?.map(range => `${range.className}(${range.studentNum}人)`).join('、') }}</span>
                          </div>
                        </div>

                        <div class="flex items-center ml-42px self-start">
                          <!-- <el-button class="border text-[#6474FD] border-[#6474FD] px-6px mr-10px" @click="handleAddSubject(item, element)">
                            <svg-ri-edit-2-fill />
                            <span class="pl-4px">编辑</span>
                          </el-button> -->
                          <div
                            class="text-[#FF4646] cursor-pointer w-50px h-30px leading-[30px] border-[1px] border-solid border-[#FF4646] text-center rounded-[4px] "
                            @click="deleteTaskApi(element)"
                          >
                            删除
                          </div>
                          <svg-ri-draggable v-if="item.taskList?.length > 1" class="ml-12px cursor-move handle" />
                        </div>
                      </div>
                    </template>
                  </Draggable>
                  <el-button
                    v-if="item.taskList?.length < 5"
                    class="text-[#6474FD] border border-[#DADDE8] rounded-[6px] h-[51px] flex-1 flex justify-center cursor-pointer "
                    :class="[{ '!cursor-not-allowed !text-[#999]': !creatFlag }, item.taskList?.length ? 'w-179px' : 'w-full']"
                    :disabled="!creatFlag"
                    @click="handleAddSubject(item)"
                  >
                    <svg-ri-add-large-line class="mr-9px" />
                    <span class="text-[15px]">新增</span>
                  </el-button>
                </div>
              </div>
            </div>
          </template>
          <template v-if="!loading && !subjectList?.length">
            <g-empty description="请先完成任务名称、时间及模式设置~"></g-empty>
          </template>
        </div>
      </el-scrollbar>
    </div>
    <div class="foot-bar h-[58px]">
      <el-button
        type="primary"
        class="text-15px px-22px h-30px"
        :disabled="!hasResourceTask"
        @click="router.back()"
      >
        下一步
      </el-button>
    </div>

    <UpdateTask
      v-model:show="showDialog"
      :task-id="editTaskId"
      @update-list="updateList"
      @click.stop
    ></UpdateTask>
  </div>
</template>

<style lang="scss" scoped>
.foot-bar{
  height: 58px;
  background: #fff;
  margin: 0 -26px ;
  box-shadow: 0px -2 7px 0px rgba(0,0,0,0.04);
  text-align: right;
  padding-top: 14px;
  padding-right: 26px;
}
</style>
