<script setup lang="ts">
interface Data {
  items: DataType[]
  data: any
}
interface DataType {
  title: string // 标题
  slotName: string // 插槽名称
  unit?: string // 单位
  [key: string]: any
}

defineProps({
  dataOption: {
    type: Object as PropType<Data>,
    default: () => ({}),
  },
})
</script>

<template>
  <div v-if="dataOption.items?.length" class="bg-[white] br-[6px] py-26px flex flex-nowrap overflow-hidden">
    <div
      v-for="(item, index) in dataOption.items"
      :key="index"
      class="flex-1 relative"
      :class="{ itemLine: index < dataOption.items.length - 1 }"
    >
      <div class="text-center dataTop mb-6px">
        <slot
          :name="item.slotName"
          :item="item"
          :value="dataOption.data[item.slotName]"
          :data="dataOption.data"
        >
          <span class="text-26px font-600" :class="[item.dataClass]"> {{ dataOption.data[item.slotName] ?? '/' }}</span>
          <span
            v-if="item.unit && dataOption.data[item.slotName]"
            class="text-15px font-400"
            :class="[item.unitClass]"
          >
            {{ item.unit }}
          </span>
        </slot>
      </div>
      <p class="text-center text-13px text-[#929296]" :class="[item.titleClass]">
        {{ item.title }}
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.itemLine::after{
  position: absolute;
  top: 10px;
  right: 0;
  display: block;
  width: 1px;
  height: 26px;
  background: #ccc;
  content: '';
}
</style>
