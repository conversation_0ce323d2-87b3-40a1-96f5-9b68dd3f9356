<script setup lang="ts">
import {
  getAnswerListApi,
  getStudentDetailApi,
  getStudentsApi,
  submitAnswerListApi,
} from '@/api/comprehensiveTask'
import BScroll from '@better-scroll/core'
import CorrectDialog from './components/CorrectDialog.vue'

let currentImgObject = $ref<any>(null)
const route = useRoute()
let showCorrectDialog = $ref<any>(false)
let loading = $ref<any>(false)
let studentInfoArr = $ref<any>([
  {
    label: '启鸣号',
    value: null,
  },
  {
    label: '学生姓名',
    value: null,
  },
  {
    label: '学生班级',
    value: null,
  },
  {
    label: '任务时间',
    value: null,
  },
  {
    label: '任务时长',
    value: null,
  },
])
let studentList = $ref<any>([])
let currentStudent = $ref<any>(null)
let contentList = $ref<any>([])
let contentIndex = $ref<number>(0)// 任务索引，单个图片标注调用提交接口时用到
function onStudentClick(student) {
  currentStudent = student?.schoolStudentId
}
function onImageClick(img, content, index) {
  showCorrectDialog = true
  currentImgObject = {
    ...img,
    contentId: content.sysSubjectId,
    imageList: content.imageList,
    showImageList: content.showImageList,
  }
  contentIndex = index
}
async function getStudents() {
  if (!route?.query?.taskScheduleGroupId && !route?.query?.taskId)
    return

  const res = await getStudentsApi({
    taskScheduleGroupId: route?.query?.taskScheduleGroupId,
    taskId: route?.query?.taskId,
  })
  studentList = res || []
  if (!currentStudent)
    currentStudent = route.query?.schoolStudentId

  await nextTick()
  const container = document.getElementById('active')
  if (container) {
    container.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    })
  }
}
async function getAnswerList() {
  loading = true
  const res = await getAnswerListApi({
    taskScheduleGroupId: route?.query?.taskScheduleGroupId,
    schoolStudentId: currentStudent,
    taskId: route?.query?.taskId,
  })
  loading = false
  contentList = res || []
  contentList.map((item) => {
    item.imageList = item?.images.map((item1) => {
      return {
        ...item1,
        thumbnailUrl: item1?.commentImage,
      }
    })
    item.showImageList = item?.images.map((item) => {
      item.noteData = null
      return item
    })
    return item
  })
  await nextTick()
  contentList.map((item, index) => {
    item.bs = null
    startRoll(index + 1, item)
  })
}
async function startRoll(id, item) {
  const box = document.getElementById(id)
  if (item.bs == null && box) {
    item.bs = new BScroll(box, {
      scrollX: true,
      click: true,
    })
  }
  else {
    item.bs?.refresh()
    item.bs?.scrollTo(0, 0, 500)
  }
}
watch(
  () => currentStudent,
  () => {
    if (currentStudent) {
      getStudentDetail()
      getAnswerList()
    }
  },
)
async function getStudentDetail() {
  const res = await getStudentDetailApi({
    taskScheduleGroupId: route?.query?.taskScheduleGroupId,
    schoolStudentId: currentStudent,
    taskId: route?.query?.taskId,
  })
  studentInfoArr[0].value = res?.idNum
  studentInfoArr[1].value = res?.schoolStudentName
  studentInfoArr[2].value = $g._.map(res?.studentClassList, 'className').join('、')
  studentInfoArr[3].value = {
    start: res?.startTime,
    end: res?.endTime,
  }
  studentInfoArr[4].value = res?.taskDuration
}
function editImges(val, val1, val3) {
  const index = val1.value
  if (val == 'clear') {
    currentImgObject.noteData = null
    currentImgObject.commentImage = currentImgObject?.sourceImage
    currentImgObject.thumbnailUrl = currentImgObject?.sourceImage
    currentImgObject.isAnnotation = 1
    currentImgObject.showImageList[index].noteData = null
    currentImgObject.imageList[index].commentImage = currentImgObject.imageList[index]?.sourceImage
    currentImgObject.imageList[index].thumbnailUrl = currentImgObject.imageList[index]?.sourceImage
    currentImgObject.imageList[index].isAnnotation = 1
    return
  }
  currentImgObject.imageList = val
  currentImgObject.imageList.map((item) => {
    item.commentImage = item.thumbnailUrl
    return item
  })
  currentImgObject.showImageList[index].noteData = val3
}
async function submitAnswer(val1) {
  const {
    taskResourceLearnId,
    isPass,
  } = val1
  let res = await submitAnswerListApi({
    taskResourceLearnId,
    isPass,
    comments: $g._.map(val1.imageList, 'commentImage').join(','),
  })
  $g.msg('批改成功', 'success')
}
async function handleChange(val) {
  val.updateTime = $g.dayjs().format('YYYY-MM-DD HH:mm:ss')
  studentList.map((student) => {
    if (student.schoolStudentId == currentStudent)
      student.correctionState = 3

    return student
  })
  await submitAnswer(val)
  await getStudents()
}
async function submitOfImg() {
  let params = {
    ...contentList[contentIndex],
    isPass: contentList[contentIndex].isPass,
    updateTime: $g.dayjs().format('YYYY-MM-DD HH:mm:ss'),
  }

  await submitAnswer(params)
  getStudents()
}
onMounted(() => {
  getStudents()
})
</script>

<template>
  <div class="p-[26px]">
    <g-navbar title="默写批改"></g-navbar>
    <div class="flex mt-[26px]">
      <div class="w-[221px]">
        <div class="w-full h-[325px] overflow-y-auto bg-[#FFFFFF] mb-[17px] p-[17px] br-[6px]">
          <div class="text-[15px] font-500 text-[#333333]">
            学生信息
          </div>
          <div
            v-for="(sInfo, sIndex) in studentInfoArr"
            :key="sIndex"
            class="mt-[17px]"
          >
            <span
              v-if="sInfo.label == '任务时间'"
              class="text-[14px] font-500 text-[#333333]"
            >
              {{ sInfo?.label }}：
              <div class="flex items-center mt-[12px]">
                <img
                  class="w-[15px] h-[15px] mr-[4px]"
                  src="@/assets/img/correctionPage/startTime.png"
                /><span>{{ sInfo?.value?.start ? $g.dayjs(sInfo?.value?.start).format('YYYY/MM/DD HH:mm:ss') : '-' }}</span>
              </div>
              <div class="flex items-center mt-[14px]">
                <img
                  class="w-[15px] h-[15px] mr-[4px]"
                  src="@/assets/img/correctionPage/endTime.png"
                /><span>{{ sInfo?.value?.end ? $g.dayjs(sInfo?.value?.end).format('YYYY/MM/DD HH:mm:ss') : '-' }}</span>
              </div>
            </span>
            <span
              v-else-if="sInfo.label == '任务时长'"
              class="text-[14px] font-500 text-[#333333]"
            >
              {{ sInfo?.label }}：<span class="text-[14px] text-[#6474FD]">
                {{
                  sInfo?.value || '-'
                }}
              </span>
            </span>
            <span
              v-else
              class="text-[14px] font-500 text-[#333333]"
            >
              {{ sInfo?.label }}：<span class="text-[14px] text-[#6C6C74]">
                {{
                  sInfo?.value || '-'
                }}
              </span>
            </span>
          </div>
        </div>
        <div :style="{ height: 'calc(100vh - 460px)' }" class="w-full overflow-y-auto bg-[#FFFFFF] p-[17px] br-[6px]">
          <div class="text-[15px] font-500 mb-[17px] text-[#333333]">
            同期学生
          </div>
          <div
            v-for="(student, studentIndex) in studentList"
            :id="currentStudent == student?.schoolStudentId ? 'active' : ''"
            :key="studentIndex"
            :class="{
              'bg-[rgba(100,116,253,0.1)] br-[4px] border-b-[1px] border-[#FFFFFF]':
                currentStudent == student?.schoolStudentId,
              'border-b-[1px] border-[#E8E8E8]':
                currentStudent != student?.schoolStudentId,
            }"
            class="flex text-[14px] font-500 justify-between px-[17px] py-[11px]"
            @click="onStudentClick(student)"
          >
            <div>
              {{ student?.schoolStudentName }}
            </div>
            <div
              class="text-[15px] font-500 flex-shrink-0"
              :class="{
                'text-theme-success': student.correctionState == 3,
                'text-theme-warning': student.correctionState == 2,
                'text-theme-error': student.correctionState == 1,
              }"
            >
              {{
                student.correctionState == 1
                  ? '未批改'
                  : student.correctionState == 2
                    ? '批改中'
                    : '已批改'
              }}
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex-1 overflow-y-auto no-bar"
        :style="{ height: 'calc(100vh - 112px)', width: 'calc(100vw - 273px)' }"
      >
        <g-loading v-if="loading" class="h-[200px]">
        </g-loading>
        <template v-else>
          <template v-if="contentList?.length">
            <div
              v-for="(content, contentIndex) in contentList"
              :key="contentIndex"
              class="bg-[#FFFFFF] br-[6px] p-[17px] mb-[17px] ml-[15px]"
            >
              <div class="text-[15px] font-500 text-[#333333] mb-[17px]">
                {{ content.sysSubjectName }}
              </div>
              <div :id="String(contentIndex + 1)" class="flex overflow-hidden relative">
                <div class="w-fit flex flex-wrap gap-[13px]">
                  <div
                    v-for="(img, imgIndex) in content?.imageList"
                    :key="imgIndex"
                    class="flex-shrink-0 border-[1px] border-[#E8E8E8FF] w-[107px] h-[107px]  br-[6px] relative cursor-pointer overflow-hidden"
                    @click="onImageClick(img, content, contentIndex)"
                  >
                    <img :src="img?.commentImage" class="w-[107px] object-cover  mr-[17px] h-full" />
                    <img
                      v-if="img?.isAnnotation == 2"
                      src="@/assets/img/taskCenter/correct.png"
                      class="w-[38px] absolute top-0  left-0 h-[18px]"
                    />
                  </div>
                </div>
              </div>
              <div class="w-full h-[1px] bg-[#E8E8E8] my-[17px]"></div>
              <span class="text-[15px] text-[#333333]">任务批改</span>
              <div class="">
                <div class="flex items-center ">
                  <div class="text-[14px] text-[#999]">
                    是否合格：
                  </div>
                  <el-radio-group
                    v-model="content.isPass"
                    :disabled="content.isPass"
                    class="ml-6px"
                    @change="handleChange(content)"
                  >
                    <el-radio :value="2">
                      合格
                    </el-radio>
                    <el-radio :value="1">
                      不合格
                    </el-radio>
                  </el-radio-group>
                  <!-- <el-input-number
                    v-model="content.finallyScore"
                    :controls="false"
                    :min="0"
                    :max="100"
                    class="w-[59px]"
                    @change="handleChange($event, content)"
                  /><span class="ml-[9px] text-[#999999] text-[15px]">分</span> -->
                </div>
                <!-- <div class="text-[15px]">
                  是否合格：<span class="text-[15px] text-[#6C6C74]">{{ content?.isPass == 2 ? '合格' : content?.isPass == 1 ? '不合格' : '-' }}</span>
                </div> -->
                <div class="text-[15px] ">
                  <span class="text-[14px] text-[#999]">批改时间：</span>
                  <span class="text-[15px] text-[#6C6C74]">{{ content?.updateTime ? $g.dayjs(content?.updateTime).format('YYYY/MM/DD HH:mm:ss') : '-' }}</span>
                </div>
              </div>
            </div>
          </template>
          <g-empty v-else></g-empty>
        </template>
      </div>
    </div>
    <CorrectDialog
      v-model="showCorrectDialog"
      :img-list="currentImgObject?.imageList"
      :show-image-list="currentImgObject?.showImageList"
      :current-img-object="currentImgObject"
      @edit-imges="editImges"
      @submit-of-img="submitOfImg"
    ></CorrectDialog>
  </div>
</template>

<style lang="scss" scoped>
</style>
