<script setup lang="ts" name="ComprehensiveTask">
import {
  createScheduleName,
  deleteComprehensiveTask,
  getScheduleData,
  scheduleClose,
  scheduleStateList,
} from '@/api/comprehensiveTask'
import DataPresentation from './components/DataPresentation.vue'
import SearchFrom from './components/SearchForm.vue'

const router = useRouter()
let publishTimeList: any = $ref([
  {
    id: -1,
    title: '全部',
    beginTime: null,
    endTime: null,
  },
  {
    id: 0,
    title: '今',
    ...getLastNDaysRange(1),
  },
  {
    id: 1,
    title: '昨',
    ...getLastNDaysRange(2, false),
  },
  {
    id: 2,
    title: '近7天',
    ...getLastNDaysRange(7),
  },
  {
    id: 3,
    title: '近30天',
    ...getLastNDaysRange(30),
  },
]) // 布置时间列表
let publishTime: any = $ref([]) // 具体布置时间
const STATE_TYPE = {
  1: {
    text: '待发布',
    className: 'text-[#FF7D29]',
  },
  2: {
    text: '生效中',
    className: 'text-[#00B34A]',
  },
  3: {
    text: '已完成',
    className: 'text-[#00B34A]',
  },
  4: {
    text: '已关闭',
    className: 'text-[#999]',
  },
  // 5: { text: '待发布', className: 'text-[#999]' },
}

// let dataList = [
//   { title: '总任务数', value: 0 },
//   { title: '任务完成率', value: null },
//   { title: '测验正确率', value: null },
//   { title: '平均任务时长', value: null },
// ]
const formOptions: any = $ref({
  items: {
    publishTime: {
      label: '任务时间',
      slot: true,
    },
    // class: {
    //   label: '任务班级',
    //   idName: 'id',
    //   labelName: 'title',
    //   list: [],
    // },
    state: {
      label: '任务状态',
      idName: 'id',
      labelName: 'title',
      list: [],
    },
    keyword: {
      label: '搜索任务',
      slot: true,
    },
  },
  data: {
    currentPublishTime: -1,
    state: '',
    keyword: '',
    class: '',
  },
})
const tableOptions = reactive<any>({
  loading: true,
  ref: null as any,
  column: [
    {
      label: '任务名称',
      prop: 'scheduleName',
      minWidth: 160,
    },
    // {
    //   label: '班级',
    //   prop: 'class',
    //   minWidth: 100,
    // },
    {
      label: '生效时间',
      prop: 'effectTime',
      minWidth: 160,
      formatter(row) {
        if (!row?.effectTime)
          return '/'

        return $g.dayjs(row?.effectTime).format('YYYY/MM/DD HH:mm:ss')
      },
    },
    {
      label: '任务进度',
      prop: 'progress',
      slot: true,
      minWidth: 140,
    },
    {
      label: '任务完成用时',
      prop: 'totalDurationStr',
      slot: true,
      minWidth: 160,
    },
    {
      label: '创建人',
      prop: 'accountAdminName',
      minWidth: 100,
    },
    {
      label: '任务状态',
      prop: 'state',
      slot: true,
      minWidth: 100,
    },
    {
      label: '操作',
      prop: 'cz',
      slot: true,
      fixed: 'right',
      minWidth: 110,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    pageSize: 10,
    total: 0,
  },
})

// 秒钟转换时分秒
function secondsToMinutesSeconds(seconds) {
  if (!seconds) return '/'
  const hours = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  const hourText = hours ? `${hours}时` : ''
  const minsText = mins ? `${mins}分` : ''
  const secsTExt = secs ? `${secs}秒` : ''
  return `${hourText + minsText + secsTExt}`
}

/**
 * @description 获取近n天的时间/前n天当天的时间
 * @param days 天数
 * @param all  true取近n天的时间   false取前n天当天的时间
 */
function getLastNDaysRange(days, all = true) {
  // 获取今天的时间
  const today = $g.dayjs()
  // 获取N天前的时间
  const nDaysAgo = today.subtract(days - 1, 'day')
  // 获取N天前的开始时间（00:00:00）
  const startOfNDaysAgo = nDaysAgo.startOf('day')
  // 获取N天前的结束时间（00:00:00）
  const endOfNDaysAgo = nDaysAgo.endOf('day')
  // 获取今天的结束时间（23:59:59）
  const endOfToday = today.endOf('day')
  // 格式化时间为字符串
  const startOfDayStr = startOfNDaysAgo.format('YYYY-MM-DD HH:mm:ss')
  const endOfDayStr = all ? endOfToday.format('YYYY-MM-DD HH:mm:ss') : endOfNDaysAgo.format('YYYY-MM-DD HH:mm:ss')
  // 返回结果对象
  return {
    beginTime: startOfDayStr,
    endTime: endOfDayStr,
  }
}

// 布置时间切换
function timeChange(item) {
  if (!publishTime)
    publishTime = []

  publishTime[0] = publishTimeList[item + 1].beginTime
  publishTime[1] = publishTimeList[item + 1].endTime
  initPage()
}

// 日期选择
function dateChoose() {
  if (!publishTime)
    formOptions.data.currentPublishTime = publishTimeList[0].id

  else
    formOptions.data.currentPublishTime = null

  initPage()
}

const commandHandler = $g._.throttle((type, row) => {
  const commands = {
    updateTask,
    checkTask,
    closeTask,
    copyTask,
    publishTask,
    deleteTask,
  }
  commands[type](row)
}, 1000, {
  leading: true,
  trailing: false,
})

// 获取任务状态列表
async function getTaskStatusList() {
  const res = await scheduleStateList()
  res.unshift({
    id: '',
    title: '全部',
  })
  formOptions.items.state.list = res
}

// 获取任务概况
function getTaskOverview() {}

function initPage() {
  tableOptions.pageOptions.page = 1
  getScheduleDataApi()
}

// 获取任务列表
async function getScheduleDataApi() {
  try {
    tableOptions.loading = true
    const {
      page,
      pageSize,
    } = tableOptions.pageOptions
    const {
      state,
      keyword,
    } = formOptions.data
    const res = await getScheduleData({
      page,
      pageSize,
      state,
      keyword,
      beginTime: publishTime?.[0] || null,
      endTime: publishTime?.[1] || null,
    })
    res.list.forEach((i) => {
      i.taskType = '综合任务'
      i.totalDurationStr = i.state == 4 ? '/' : secondsToMinutesSeconds(i.totalDuration)
      i.progress = $g.math(i.completeNum).divide(i.totalNum).multiply(100).toFixed().value()
    })
    tableOptions.data = res?.list || []
    tableOptions.pageOptions.total = res?.total || 0
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
    tableOptions.data = []
    tableOptions.pageOptions.total = 0
  }
  finally {
    tableOptions.loading = false
  }
}
// 发布任务
async function publishTask(row) {
  console.log('⚡[ row ] >', row)
  if (!row?.scheduleName || !row?.effectTime || !row?.effectTime) return
  await createScheduleName({
    scheduleName: row?.scheduleName,
    releaseTime: row?.effectTime,
    requireCompleteTimer: row?.effectTime,
  })
  getScheduleDataApi()
}
// 删除任务
async function deleteTask(row) {
  $g.confirm({
    content: '确定删除该任务？删除后不可恢复。',
  }).then(async () => {
    await deleteComprehensiveTask({ id: row?.taskScheduleId })
    getScheduleDataApi()
  }).catch((err) => {
      console.log(err)
    })
}
// 修改任务
function updateTask(row) {
  router.push({
    name: 'ComprehensiveTaskCreate',
    query: { taskScheduleId: row.taskScheduleId },
  })
}
// 进入任务详情页
function checkTask(row) {
  router.push({
    name: 'ComprehensiveTaskCreate',
    query: { taskScheduleId: row.taskScheduleId },
  })
}

// 关闭任务
async function closeTask(row) {
  try {
    await scheduleClose({ taskScheduleId: row.taskScheduleId })
    row.state = 4
    $g.msg('任务关闭成功！', 'success')
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
    $g.msg('任务关闭失败！', 'error')
  }
}

// 复制任务------赞不支持该功能
function copyTask(row) { console.log('⚡[ copyTask ] >') }

// 进入学生任务列表
function toStudentList(row) {
  router.push({
    name: 'ComprehensiveTaskTaskOverview',
    query: { taskScheduleId: row.taskScheduleId },
  })
}

// 创建任务
function handleClick() {
  router.push({
    name: 'ComprehensiveTaskCreate',
    query: { taskSource: 'zhrw' },
  })
}

onMounted(() => {
  getTaskStatusList()
  getTaskOverview()
  getScheduleDataApi()
})

onActivated(() => {
  getTaskOverview()
  getScheduleDataApi()
})
</script>

<template>
  <div class="p-26px" style="width: 100vw;">
    <g-navbar title="任务概况">
    </g-navbar>
    <!-- <DataPresentation class="mt-26px" :data-list="dataList" /> -->
    <div class="mt-26px br-[6px] p-17px bg-[white]">
      <SearchFrom :form-option="formOptions" @change="initPage">
        <template #publishTime>
          <el-radio-group v-model="formOptions.data.currentPublishTime" @change="timeChange">
            <el-radio
              v-for="item in publishTimeList"
              :key="item.id"
              :value="item.id"
            >
              <span class="!font-400 !text-[#333]"> {{ item.title }}</span>
            </el-radio>
          </el-radio-group>
          <div class="w-[386px] h-25px ml-10px">
            <el-date-picker
              ref="pickerRef"
              v-model="publishTime"
              class="-mt-[3px] h-30px w-full"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="dateChoose"
            />
          </div>
        </template>
        <template #keyword>
          <el-input
            v-model="formOptions.data.keyword"
            style="width: 181px"
            class="h-34px"
            clearable
            placeholder="请输入任务名称"
          />
          <el-button
            color="#6474FD"
            class="w-64px ml-6px h-[34px] border-none"
            @click="initPage()"
          >
            搜索
          </el-button>
        </template>
      </SearchFrom>
    </div>
    <div class="mt-17px br-[6px] p-17px bg-[white]">
      <div class="flex justify-between items-center">
        <div>
          <p class="font-600">
            任务列表
          </p>
          <p class="text-13px text-[#999999] mt-6px">
            更新时间: 实时统计
          </p>
        </div>
        <button class="px-13px br-[17px] border border-[#6474FD] text-[#6474FD] h-34px leading-[34px] flex items-center text-13px" @click="handleClick">
          <img
            :src="$g.tool.getFileUrl('comprehensiveTask/add.png')"
            class="w-17px h-17px mr-4px"
            alt="add"
          >
          <span>创建任务</span>
        </button>
      </div>

      <g-table
        stripe
        :border="false"
        :header-cell-style="{
          background: '#6474FD1A',
          color: '#6C6C74',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :cell-style="{
          color: '#333333',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :table-options="tableOptions"
        :highlight-current-row="false"
        @change-page="getScheduleDataApi"
      >
        <!-- 任务进度 -->
        <template #progress="{ row }">
          <div class="flex">
            <el-progress
              :percentage="row.progress"
              :show-text="false"
              :stroke-width="9"
              class="flex-1 mr-4px"
            />
            <span class="text-13px">{{ row.completeNum }}/{{ row.totalNum }}</span>
          </div>
        </template>
        <!-- 任务完成用时 -->
        <template #totalDuration="{ row }">
          <span>{{ row.totalDuration || '/' }}</span>
        </template>
        <!-- 任务状态 -->
        <template #state="{ row }">
          <span :class="STATE_TYPE[row.state].className">{{ STATE_TYPE[row.state].text }}</span>
        </template>
        <template #cz="{ row }">
          <el-button
            type="primary"
            class="p-0"
            text
            :disabled="row.state == 1"
            @click="toStudentList(row)"
          >
            查看
          </el-button>
          <el-dropdown
            trigger="click"
            placement="top"
            @command="commandHandler($event, row)"
          >
            <el-button
              type="primary"
              class="!outline-none p-0 ml-12px"
              text
            >
              操作
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="row.state == 1" command="updateTask">
                  修改任务
                </el-dropdown-item>
                <el-dropdown-item v-else command="checkTask">
                  查看任务
                </el-dropdown-item>
                <el-dropdown-item v-if="[1, 2].includes(row.state)" command="closeTask">
                  关闭任务
                </el-dropdown-item>
                <el-dropdown-item command="publishTask">
                  <!-- v-if="row.state == 5" -->
                  发布任务
                </el-dropdown-item>
                <el-dropdown-item v-if="[1, 4, 5].includes(row.state)" command="deleteTask">
                  删除任务
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </g-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(){
.el-table .el-table__body tr:hover > td.el-table__cell{
    background-color: inherit !important;
}
.el-button.is-text{
  background-color: transparent !important;
}
}
</style>
