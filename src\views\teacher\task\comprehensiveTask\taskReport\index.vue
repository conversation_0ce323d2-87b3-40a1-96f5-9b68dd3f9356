<script setup lang="ts">
import { getExerciseReport } from '@/api/taskCenter'

const route = useRoute()

let loading = $ref<boolean>(true)
let reportList = $ref<any>([])
let getCorrectRate = $computed(() => {
  return (val) => {
    return $g.math(val).multiply(100).toFixed(0).value()
  }
})
let getDateTime = $computed(() => {
  return (val, type = 'day') => {
    if (type === 'day') {
      return $g.dayjs(val).format('MM-DD')
    }
    else if (type === 'time') {
      return $g.dayjs(val).format('HH:mm')
    }
    else if (type === 'second') {
      let minte = Math.floor(val / 60)
      let second = val % 60
      return `${minte ? `${minte}分` : ''}${second}秒`
    }
  }
})
let getTimes = $computed(() => {
  return (index) => {
    if (index == 0) {
      return '首次'
    }
    else {
      return `第${index + 1}轮`
    }
  }
})
async function getExerciseReportApi() {
  try {
    loading = true
    const { taskId, accountId } = route.query
    if (!taskId || !accountId) {
      loading = false
      return
    }
    let res = await getExerciseReport({ taskId, accountId })
    reportList = res?.[0]?.exerciseTask
    loading = false
  }
  catch (error) {
    loading = false
    console.log(error)
  }
}
onMounted(() => {
  getExerciseReportApi()
})
</script>

<template>
  <div class="p-26px" style="width: 100vw;">
    <g-navbar title="报告页" class="mb-17px">
    </g-navbar>

    <div class="bg-white p-[17px] rounded-[6px]">
      <g-loading v-if="loading" class="h-200px"></g-loading>
      <template v-else>
        <g-empty v-if="reportList"></g-empty>
        <div class="relative">
          <div
            class="text-[14px] text-[#636772] leading-[20px] absolute top-4px right-0"
          >
            更新时间：实时统计
          </div>
          <div v-for="(item, index) in reportList" :key="item">
            <div class="h-[115px] flex gap-[14px]" :class="{ '!h-[145px]': $g.isPC }">
              <div class="text-[15px]  w-[60px] text-[#333] leading-[21px] font-600">
                {{ getTimes(index) }}
              </div>
              <div
                class="h-inherit  relative mt-6px"
                :class="{ 'border-r border-dashed border-[#CCCCCC]': index + 1 != 5 }"
              >
                <div
                  class="w-6px h-6px bg-[#CCCCCC] rounded-[6px] absolute top-0 left-[-3px]"
                ></div>
              </div>
              <div
                class="min-h-[98px]  border rounded-[4px] w-[563px] border-[#DCDFE6] bg-[rgba(216,216,216,0.08)] mb-[17px] mt-6px p-17px cursor-pointer flex flex-col justify-between"
              >
                <div class="flex justify-between items-center">
                  <div class="flex flex-1 items-center">
                    <div
                      class=" h-[29px] w-fit px-[6px] border  text-center rounded-[4px] leading-[29px] text-[13px] text-[#6474FD] border-[rgba(100,116,253,0.14)] bg-[#E3F5FF]"
                    >
                      检查
                    </div>
                    <div
                      class="ml-[13px] text-[15px] text-[#333]  font-[500] w-[300px] overflow-hidden text-ellipsis whitespace-nowrap"
                    >
                      {{ item?.exerciseTaskName ?? '-' }}
                    </div>
                    <div class="w-[127px] h-18px accuracy relative">
                      <div
                        class="text-[26px] text-[#6474FD] leading-[30px] absolute bottom-[-5px] right-[6px]"
                      >
                        {{ getCorrectRate(item?.correctRate ?? 0)
                        }}<span class="!text-[16px]">%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="flex justify-between items-center text-[13px] text-[#999999] leading-[18px] mt-[17px]"
                >
                  <div>提交时间：{{ getDateTime(item?.finishTime ?? 0, 'time') }}</div>
                  <div :class="{ 'flex items-center text-theme-error': item?.abnormalQuestionList?.length }">
                    <span class="leading-[14px]" @click.stop>答题时长：{{ getDateTime(item?.seconds ?? 0, 'second') }}</span>
                  </div>
                  <div class="flex items-center gap-[23px]">
                    <div>答题数：{{ item?.completeNum ?? 0 }}/{{ item?.questionNum ?? 0 }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.accuracy {
  background: url('@/assets/img/report/accuracy.png') no-repeat center /
    127px 100%;
}
</style>
