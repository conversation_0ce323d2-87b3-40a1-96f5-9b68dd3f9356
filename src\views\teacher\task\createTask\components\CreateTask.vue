<script setup lang="ts">
import {
  getStudentList,
  getTeacherClassType,
  getTeacherLastClass,
} from '@/api/taskCenter'
import { combineStudentAndGroup } from '../tool'
import CustomSelectionDialog from './CustomSelectionDialog.vue'
import QuestionTaskContent from './QuestionTaskContent.vue'
import ResourceTaskContent from './ResourceTaskContent.vue'
import StudentDrawer from './StudentDrawer.vue'

const props = defineProps({
  classType: {
    type: Number,
    default: null,
  },
})
const router = useRouter()
const route = useRoute()
const studentData = inject<Ref<any>>('studentData', ref({}))
let resourceRef = $ref<any>(null)
let isChecked = $ref<any>([])
let showDialog = $ref(false)
let classType = $ref(1)
let showStudentDrawer = $ref(false)
let classTypeList = $ref<any>([])
let studentArr = $ref<any>([])
let list = $ref<any>([])
let showExit = $ref(false)
const myTitle = $computed(() => {
  if ((route.query.pageType as any) == 1)
    return '布置学科网选题任务'

  if ((route.query.pageType as any) == 2)
    return '布置校本练习题任务'

  if ((route.query.pageType as any) == 4)
    return '综合综合'

  return '布置资源任务'
})
const isResource = $computed(() => {
  return (route.query.pageType as any) == 3 // 1为学科网选题 2为校本练习选题 3为资源类型
})

const isHilight = $computed(() => {
  return !!studentData.value.classList.length
})

async function handleChecked(val, item) {
  let find = classTypeList.find(
    h => h.classType !== item.classType && h.checked,
  )
  if (find && val) {
    if (studentData.value.selectStudentList.length) {
      $g.showConfirmDialog({
        title: '确认切换班级类型',
        message: '切换班级类型，所有选择的学生会清空，是否继续',
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        zIndex: 100,
      })
        .then(async () => {
          studentData.value = {
            classList: [],
            specialClass: null,
            selectStudentList: [],
            disabledStudentIds: [],
            disabledGroupIds: [],
          }
          isChecked = []
          find.checked = false
          classType = item.classType
          await getLastClass()
          getStudent()
        })
        .catch(() => {
          item.checked = false
        })
    }
    else {
      find.checked = false
      classType = item.classType
      await getLastClass()
      getStudent()
    }
    return
  }
  if (!val && !find) {
    item.checked = true
    classType = item.classType
  }
}

async function getClassType() {
  if (!route.query.subjectId) return
  let res = await getTeacherClassType({
    sysSubjectId: route.query.subjectId,
  })
  classTypeList = (res || []).map((h) => {
    return {
      ...h,
      checked: false,
    }
  }) || []
  if (!classTypeList.length) return
  if (props.classType) {
    classType = props.classType
    let find = classTypeList.find(h => h.classType == props.classType)
    if (find) {
      classTypeList.forEach(h => h.checked = false)
      find.checked = true
    }
    return
  }
  classTypeList[0].checked = true
  classType = classTypeList[0].classType
}

async function getLastClass() {
  if (!route.query.subjectId) return
  let res = await getTeacherLastClass({
    classType,
    taskType: route.query.pageType,
    sysSubjectId: route.query.subjectId,
  })
  if (!res) {
    list = []
    return
  }
  list = [
    {
      ...res,
      id: res.schoolClassId,
      name: res.sysGradeName + res.className,
    },
  ]
}

async function getStudent() {
  if (!list[0]?.id || !route.query.subjectId) return
  let res = await getStudentList({
    schoolClassId: list[0].id,
    sysSubjectId: route.query.subjectId,
  })
  studentArr = res || []
}

onBeforeMount(async () => {
  await getClassType()
  await getLastClass()
  getStudent()
})
function handleRadioChecked() {
  if (isChecked.length) {
    studentData.value.specialClass = {
      ...list[0],
      selectStudentArr: studentArr,
      arrangeObjectType: 1,
    }
    studentData.value.selectStudentList = [
      ...new Set([
        ...studentData.value.selectStudentList, ...studentArr.map(v => v.schoolStudentId),
      ]),
    ]
  }
  else {
    studentData.value.specialClass = null
    studentData.value.selectStudentList = [
      ...new Set(
        [
          ...combineStudentAndGroup(studentData.value.classList), ...(studentData.value.specialClass?.selectStudentArr || []),
        ].map(v => v.schoolStudentId),
      ),
    ] // 如果一个学生既在特定的班级，也在自定义选择里被选了，那么取消选中特定的班级不会取消这个学生
  }
}
function checkFileList() {
  return resourceRef?.checkFileList()
}

defineExpose({
  checkFileList,
  getRealClassType,
})
function getRealClassType() {
  return classType
}

watch(() => props.classType, (val) => {
  if (val) {
    classType = val
    let find = classTypeList.find(h => h.classType == val)
    if (find) {
      classTypeList.forEach(h => h.checked = false)
      find.checked = true
    }
  }
}, {
  immediate: true,
})
</script>

<template>
  <div class="flex flex-col h-full ">
    <g-navbar
      :title="myTitle"
      class="mb-17px"
    >
    </g-navbar>
    <div class="flex-1 flex flex-col " :class="{ 'no-bar': !$g.isPC, 'overflow-hidden': Number(route?.query?.pageType) != 3 }">
      <div class="" :class="{ 'bg-[#fff] pt-[17px] px-[17px] rounded-tl-[6px] rounded-tr-[6px]': route.query?.taskSource == 'zhrw' }">
        <div class="flex items-center">
          <span class="text-16px leading-[24px] font-600">{{ route.query?.taskSource == 'zhrw' ? '任务信息' : '1.布置对象' }}</span>
          <div v-if="classTypeList.length > 1" class="ml-39px myCheckbox">
            <el-checkbox
              v-for="item in classTypeList"
              :key="`${item.classType}classType`"
              v-model="item.checked"
              :label="item.classTypeName"
              class="text-[#74788D] mr-36px"
              @change="($event) => handleChecked($event, item)"
            />
          </div>
        </div>

        <div class="my-17px flex items-center" :class="{ '!my-10px': !$g.isPC }">
          <div v-if="route.query?.taskSource == 'zhrw'" class="mr-16px">
            布置对象
          </div>
          <g-radio
            v-model="isChecked"
            :option="list"
            item-class="px-15px  border border-[#E6E6E6] bg-[#FBFBFB] text-[#666666] text-15px h-34px flex items-center br-[6px]"
            active-item-class="!border-[#646AB4] !bg-[#ECEFFF]"
            multiply
            show-marker
            @change="handleRadioChecked"
          />
          <!-- 资源任务显示 -->
          <template v-if="route.query?.taskSource != 'zhrw'">
            <el-button
              class="ml-5px border-[#DADDE8] bg-[#FBFBFB] text-[#666666] h-34px text-15px br-[6px]"
              :class="[
                isHilight
                  ? ' !bg-[#ECEFFF]  !text-[#6474FD] !border-[#6474FD]'
                  : '',
              ]"
              @click="showDialog = true"
            >
              自定义选择
            </el-button>
            <span
              class="ml-13px text-15px  van-haptics-feedback"
              :class="studentData.selectStudentList.length ? 'text-[#6474FD]' : 'text-[#74788D]'"
              @click="showStudentDrawer = true"
            >查看已选学生({{ studentData.selectStudentList.length }})</span>
          </template>
        </div>
      </div>

      <!-- 2、学科网选题，校本练习题 任务内容 -->
      <QuestionTaskContent v-if="!isResource" />

      <!-- 资源任务 -->
      <ResourceTaskContent v-if="isResource" ref="resourceRef" />
    </div>

    <CustomSelectionDialog v-model:show="showDialog" :class-type="classType" />
    <StudentDrawer v-model="showStudentDrawer"></StudentDrawer>
  </div>
</template>

<style scoped lang="scss">
:deep(.myCheckbox) {
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #74788d;
  }
  .el-checkbox__label {
    font-size: 15px !important;
  }
}

.menu_item {
  padding: 0 16px;
  line-height: 34px;
  border-radius: 6px;
  font-size: 15px;
  border: 1px solid #dadde8;
  color: #6474fd;
  background-color: #ecefff;
}
</style>
