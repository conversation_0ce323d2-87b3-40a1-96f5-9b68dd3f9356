<script setup lang="ts">
import {
  getBookVersionListApi,
  getChapterTreeApi,
  getFromSelectApi,
  getResourceListApi,
  getSchoolBookVersionListApi,
  getSchoolChapterTreeApi,
  getTeacherGradeSelectApi,
  getTypeSelectApi,
} from '@/api/taskCenter'
import LocalTask from './LocalTask.vue'
import PreviewFile from './PreviewFile.vue'
import ResourceConditions from './ResourceConditions.vue'
import ResourcePopup from './ResourcePopup.vue'
import SubjectAndModel from './SubjectAndModel.vue'
import UploadFile from './UploadFile.vue'

let videoType = ['mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8']
let activeMenu = $ref<any>('QiMingResource') // 当前选中的菜单

let tabMenu = [
  {
    label: '启鸣资源',
    key: 'QiMingResource',
  },
  {
    label: '校本资源',
    key: 'XiaoBenResource',
  },
  {
    label: '本地资源',
    key: 'UploadResource',
  },
]
const qiMingColumn = [
  {
    label: '资源名称',
    prop: 'fileName',
    slot: true,
  },
  {
    label: '来源',
    prop: 'resourceSource',
    formatter: (row) => {
      return activeMenu == 'QiMingResource'
        ? sourceList.find(v => v.id == row.resourceSource)?.title
        : '分层教学'
    },
  },
  {
    label: '类型',
    prop: 'resourceType',
    formatter: (row) => {
      return activeMenu == 'QiMingResource'
        ? typeList.find(v => v.id == row.resourceType)?.title
        : row.fileExtension
    },
  },
  {
    label: '年级',
    prop: 'sysGradeName',
  },
  {
    label: '日期',
    prop: 'resourceDate',
    width: '100px',
    formatter: (row) => {
      return $g.dayjs(row.resourceDate).format('YYYY-MM-DD')
    },
  },
  {
    label: '操作',
    prop: 'cz',
    slot: true,
  },
]
const xiaoBenColumn = [
  {
    label: '资源名称',
    prop: 'fileName',
    slot: true,
  },
  {
    label: '来源',
    prop: 'resourceSource',
    formatter: (row) => {
      return activeMenu == 'QiMingResource'
        ? sourceList.find(v => v.id == row.resourceSource)?.title
        : '分层教学'
    },
  },
  {
    label: '类型',
    prop: 'resourceType',
    formatter: (row) => {
      return activeMenu == 'QiMingResource'
        ? typeList.find(v => v.id == row.resourceType)?.title
        : row.fileExtension
    },
  },
  {
    label: '日期',
    prop: 'resourceDate',
    width: '100px',
    formatter: (row) => {
      return $g.dayjs(row.resourceDate).format('YYYY-MM-DD')
    },
  },
  {
    label: '操作',
    prop: 'cz',
    slot: true,
  },
]
const tableOptions = reactive<any>({
  ref: null as any,
  loading: false,
  column: qiMingColumn,
  data: [],
})
let resources = inject<Ref<any[]>>('resources', ref([]))
let studentData = inject<Ref<any>>('studentData', ref({}))
let taskModel = inject<Ref<any>>('taskModel', ref(null))
let showPopup = $ref(false)
let fileList = $ref<any>([]) // 上传的资源列表
let showFile = $ref(false)
let currentFile = $ref<any>(null)
let showDialog = $ref(false)
let bookList = $ref<any>([]) // 书籍列表
let keyword = $ref<any>('') // 搜索关键词
let treeData = $ref<any>('') // 树形结构数据
let showTree = $ref(false)
let currentBookId = $ref<any>(null) // 当前选中书籍ID
let currentBookName = $ref<any>('') // 当前选中书籍名称
let showLoading = $ref(true)
const route = useRoute()
let currentCategoryId = $ref<any>(null) // 当前选中章节ID
let currentCategoryName = $ref<any>(null) // 当前选中章节名称
let resourceList = $ref<any>([]) // 当前选中章节资源列表
let gradeList = $ref<any>([]) // 年级列表
let typeList = $ref<any>([]) // 类型列表
let sourceList = $ref<any>([]) // 来源列表
let checkedCondition = $ref<any>([]) // 筛选条件
let checkedQiMingResource = $ref<any>([]) // 选中的启鸣资源
let checkedXiaoBenResource = $ref<any>([]) // 选中的校本资源
let sysTextbooksCatalogIdList = $ref<any>([]) // 校本资源接口需求参数
let addBtnDisabled = $ref<boolean>(false) // 加入按钮disabled

// 加入资源按钮disabled
let btnDisabled = $computed(() => {
  return (row) => {
    if (taskModel.value)
      return Number(taskModel.value) != 1 && videoType.includes(row.fileExtension)

    else
      return false
  }
})
const difficultyMap = {
  3: 'easy',
  4: 'ordinary',
  2: 'middle',
  1: 'difficult',
}
const conditionCount = $computed(() => {
  let count = 0
  checkedCondition.forEach((v) => {
    count += v.list.length
  })
  return count
})
fetchConditions()
async function fetchConditions() {
  try {
    let res = await Promise.all([
      getTypeSelectApi(),
      getFromSelectApi(),
      getTeacherGradeSelectApi({ sysSubjectId: route.query.subjectId }),
    ])
    typeList = res[0] || []
    sourceList = res[1] || []
    gradeList =
      res[2].map((v) => {
        return {
          ...v,
          id: v.sysGradeId,
          title: v.sysGradeName,
        }
      }) || []
  }
  catch (err) {
    console.log('获取筛选条件失败', err)
  }
}
/* 切换Tab */
async function handleChangeTab(key) {
  activeMenu = key
  showTree = false
  tableOptions.column = activeMenu == 'QiMingResource' ? qiMingColumn : xiaoBenColumn
  tableOptions.data = []
  resourceList = []
  keyword = ''
  checkedCondition = []
  if (key == 'QiMingResource')
    await fetchBook()

  else if (key == 'XiaoBenResource')
    await fetchSchoolBook()
}
/* ========= 校本资源 ========= */
/* 获取校本资源教材 */
async function fetchSchoolBook() {
  try {
    showLoading = true
    let res = await getSchoolBookVersionListApi({
      sysSubjectId: route.query.subjectId,
    })
    res?.forEach((v) => {
      v.label = `${v.sysSubjectName}-${v.sysTextbookVersionsName}${v.sysTextbooksName}`
    })
    if (activeMenu !== 'XiaoBenResource') return
    bookList = res ?? []
    currentBookId = bookList.length ? res[0].sysTextbooksId : null
    currentBookName = bookList.length ? res[0].label : ''
    currentBookId && await fetchSchoolResourceTree()
    !currentBookId && (showLoading = false)
  }
  catch (err) {
    showLoading = false
    console.log('获取校本资源教材失败', err)
  }
}
/* 获取校本资源章节树 */
async function fetchSchoolResourceTree() {
  try {
    treeData = []
    showLoading = true
    let res = await getSchoolChapterTreeApi({
      sysTextbooksId: currentBookId,
    })
    if (activeMenu !== 'XiaoBenResource') return
    treeData = res ?? []
    currentCategoryId = treeData?.[0]?.sysTextbooksCatalogId
    currentCategoryName = treeData?.[0]?.sysTextbooksCatalogName
    sysTextbooksCatalogIdList =
      treeData?.[0]?.childSysTextbooksCatalogIdList || []
    showTree = true
    showLoading = false
    currentCategoryId && await fetchSchoolResourceList()
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
  catch (err) {
    showLoading = false
    console.log('获取校本资源章节树失败', err)
  }
}
/* 获取校本资源资源列表 */
async function fetchSchoolResourceList() {
  try {
    tableOptions.loading = true
    let res = await getResourceListApi({
      sysTextbooksCatalogIdList: sysTextbooksCatalogIdList.concat([
        currentCategoryId,
      ]),
      keyword,
    })
    if (activeMenu !== 'XiaoBenResource') return
    tableOptions.data = res ?? []
    resourceList = res ?? []
    tableOptions.loading = false
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
  catch (err) {
    tableOptions.loading = false
    tableOptions.data = []
    resourceList = []
    console.log('获取校本资源资源列表失败', err)
  }
}
/* ========= 校本资源结束 ========= */
/* 点击章节 */
async function nodeClick(node) {
  currentCategoryId = node.sysTextbooksCatalogId
  currentCategoryName = node.sysTextbooksCatalogName
  if (activeMenu == 'QiMingResource') {
    tableOptions.loading = true
    setTimeout(() => {
      resourceList = node.resourceList || []
      tableOptions.data = filterData(resourceList, checkedCondition).filter(v => v.fileName.includes(keyword))
      tableOptions.loading = false
      nextTick(() => {
        $g.tool.renderMathjax()
      })
    }, 200)
  }
  else if (activeMenu == 'XiaoBenResource') {
    sysTextbooksCatalogIdList = node.childSysTextbooksCatalogIdList
    await fetchSchoolResourceList()
  }
}
/* 点击书籍 */
async function handleChangeBook(item) {
  currentBookId = item.sysTextbooksId
  currentBookName = item.label
  showTree = false
  tableOptions.data = []
  resourceList = []
  if (activeMenu == 'QiMingResource')
    await fetchResourceTree()

  else if (activeMenu == 'XiaoBenResource')
    await fetchSchoolResourceTree()
}
fetchBook()
/* =========== 启鸣资源=========== */
/* 启鸣资源-获取教材版本 */
async function fetchBook() {
  try {
    showLoading = true
    let res = await getBookVersionListApi({
      sysSubjectId: route.query.subjectId,
    })
    res?.forEach((v) => {
      v.label = `${v.sysSubjectName}-${v.sysTextbookVersionsName}${v.sysTextbooksName}`
    })
    if (activeMenu !== 'QiMingResource') return
    bookList = res ?? []
    currentBookId = bookList.length ? res[0].sysTextbooksId : null
    currentBookName = bookList.length ? res[0].label : ''
    currentBookId && await fetchResourceTree()
    !currentBookId && (showLoading = false)
  }
  catch (err) {
    showLoading = false
    console.log('获取教材版本失败', err)
  }
}
/* 启鸣资源-获取资源章节树 */
async function fetchResourceTree() {
  try {
    showLoading = true
    let res = await getChapterTreeApi({
      sysTextbookId: currentBookId,
    })
    if (activeMenu !== 'QiMingResource') return
    treeData = res || []
    currentCategoryId = treeData?.[0]?.sysTextbooksCatalogId
    currentCategoryName = treeData?.[0]?.sysTextbooksCatalogName
    resourceList = treeData?.[0]?.resourceList || []
    tableOptions.data = filterData(resourceList, checkedCondition).filter(v => v.fileName.includes(keyword))
    showLoading = false
    showTree = true
    nextTick(() => {
      $g.tool.renderMathjax()
    })
  }
  catch (err) {
    showLoading = false
    console.log('获取资源章节树失败', err)
  }
}
/* =========== 启鸣资源结束 =========== */
/* 移除年级 */
function removeGrade(index?) {
  if (index)
    checkedCondition.splice(index - 1, 1)

  else
    checkedCondition.unshift()
}
/* 表头样式 */
function headerCellClassName() {
  return {
    color: '#6C6C74',
    padding: '0px',
    top: '-2px',
  }
}
watch(
  () => showTree,
  (val) => {
    if (!val) {
      nextTick(() => {
        document.getElementById(currentBookId)?.scrollIntoView()
      })
    }
  },
)
/* 预览 */
function handlePreview(row) {
  if (!disablePreview(row)) return
  if (
    ['mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8'].includes(
      row.fileExtension,
    )
  ) {
    // if ($g.isPC) {
    currentFile = row
    showFile = true
    // }
    // else {
    //   $g.flutter('previewVideo', {
    //     url: row.fileAbsoluteUrl,
    //   })
    // }
  }
  else {
    currentFile = row
    showFile = true
  }
}
watch(
  () => showFile,
  (val) => {
    if (!val) {
      setTimeout(() => {
        currentFile = {}
      }, 350)
    }
  },
)
watch(
  () => checkedCondition,
  (val) => {
    if (activeMenu == 'QiMingResource') {
      tableOptions.data = filterData(resourceList, val).filter(v => v.fileName.includes(keyword))
      nextTick($g.tool.renderMathjax)
    }
  },
  {
    deep: true,
  },
)
/* 复制任务功能初始化数据 */
watch(
  () => studentData.value.initLoading,
  (val) => {
    if (!val) {
      if (resources.value.length)
        initData(resources.value)
    }
  },
  {
    once: true,
  },
)
watch(
  () => activeMenu,
  (val) => {
    if (val) { studentData.value.taskType = val }
  },
  { immediate: true },
)
function filterData(data, filters) {
  // 动态解析筛选条件
  const filterMap = {
    source: 'resourceSource',
    grade: 'sysGradeId',
    type: 'resourceType',
  }

  // 将筛选条件数组转换为对象，方便后续处理
  const filterConditions = filters.reduce((acc, filter) => {
    acc[filter.type] = filter.list.map(item => item.id || item.sysGradeId)
    return acc
  }, {})

  // 过滤数据
  return data.filter((item) => {
    // 检查每个筛选条件
    return Object.keys(filterConditions).every((filterKey) => {
      const validIds = filterConditions[filterKey]
      const filterField = filterMap[filterKey]
      if (!filterField)
        return true // 如果没有该字段，直接通过

      return validIds.includes(item[filterField]) // 检查数据项是否符合筛选条件
    })
  })
}
/* 搜索 */
async function search() {
  if (activeMenu == 'QiMingResource') {
    if (!$g.tool.isTrue(keyword)) {
      tableOptions.data = filterData(resourceList, checkedCondition)
      return
    }
    tableOptions.data = tableOptions.data.filter(v =>
      v.fileName.includes(keyword))
  }
  else {
    currentCategoryId && await fetchSchoolResourceList()
  }
}
/* 选中资源ID */
const checkedIdList = $computed(() => {
  return checkedQiMingResource
    .map(v => v.resourceList.map(v => v.resourceId))
    .flat()
    .concat(
      checkedXiaoBenResource
        .map(v => v.resourceList.map(v => v.resourceId))
        .flat(),
    )
})
/* 选中数据 */
function selected(row, type = activeMenu) {
  if (!disablePreview(row)) return
  let arr = [] as any
  if (type == 'QiMingResource')
    arr = checkedQiMingResource

  else
    arr = checkedXiaoBenResource

  let item = arr.find(v => v.bookId == (row.sysTextbooksId || currentBookId))

  if (item) {
    let index = item.resourceList.findIndex(
      v => v.resourceId == row.resourceId,
    )
    if (index != -1) {
      item.resourceList.splice(index, 1)
      if (!item.resourceList.length) {
        arr.splice(
          arr.findIndex(v => v.bookId == (row.sysTextbooksId || currentBookId)),
          1,
        )
      }
    }
    else {
      item.resourceList.push({
        sysTextbooksId: currentBookId,
        sysTextbooksCatalogId: currentCategoryId,
        sysTextbooksCatalogName: currentCategoryName,
        sysTextbooksName: currentBookName,
        ...row,
      })
    }
  }
  else {
    arr.push({
      bookId: currentBookId,
      bookName: currentBookName,
      resourceList: [
        {
          sysTextbooksId: currentBookId,
          sysTextbooksName: currentBookName,
          sysTextbooksCatalogId: currentCategoryId,
          sysTextbooksCatalogName: currentCategoryName,
          ...row,
        },
      ],
    })
  }
}
const count = $computed(() => {
  let num = 0
  checkedQiMingResource.forEach((v) => {
    num += v.resourceList.length
  })
  checkedXiaoBenResource.forEach((v) => {
    num += v.resourceList.length
  })
  return num + fileList.length
})
watch(
  () => [checkedQiMingResource,
checkedXiaoBenResource,
fileList],
  () => {
    // 将checkedQiMingResource和checkedXiaoBenResource中每一项的resourceList组合成新的数组赋值给resources
    resources.value = checkedQiMingResource
      .map(v => v.resourceList)
      .flat()
      .concat(checkedXiaoBenResource.map(v => v.resourceList).flat())
      .concat(
        fileList.map((v) => {
          return {
            ...v,
            resourceSource: 5,
            fileDuration: v?.fileDuration ?? '',
            fileSize: v?.fileSize ?? '',
            fileName: v?.fileName ?? '',
            fileAbsoluteUrl: v?.fileAbsoluteUrl ?? '',
          }
        }),
      )
  },
  {
    deep: true,
  },
)

function checkFileList() {
  let status = fileList.some(v => v.status != 'success')
  if (status)
    $g.showToast('请等待文件上传完成')

  return status
}

function openResourcePopup() {
  showPopup = true
}
/* 处理数据 */
function initData(val) {
  val.forEach((v) => {
    if (v.resourceSource == 5) {
      fileList.push({
        ...v,
        name: v.fileName,
        percentage: 100,
        status: 'success',
        fullUrl: v.fileAbsoluteUrl,
        duration: v.fileDuration,
        size: v.fileSize,
        resource_url: v.fileAbsoluteUrl,
      })
    }
    else if (v.resourceSource == 4) {
      let item = checkedXiaoBenResource.find(item => item.bookId == v.sysTextbooksId)
      if (item) {
        item.resourceList.push(v)
      }
      else {
        checkedXiaoBenResource.push({
          bookId: v.sysTextbooksId,
          bookName: v.sysTextbooksName,
          resourceList: [v],
        })
      }
    }
    else {
      let item = checkedQiMingResource.find(item => item.bookId == v.sysTextbooksId)
      if (item) {
        item.resourceList.push(v)
      }
      else {
        checkedQiMingResource.push({
          bookId: v.sysTextbooksId,
          bookName: v.sysTextbooksName,
          resourceList: [v],
        })
      }
    }
  })
}
// 默写模式只能上传一份资源
watch(() => resources.value, () => {
  addBtnDisabled = (Number(taskModel) == 2 || taskModel.value == 2) && resources.value.length > 0
}, {
  deep: true,
  immediate: true,
})

//
watch(() => taskModel.value, () => {
  checkedQiMingResource = []
  checkedXiaoBenResource = []
  fileList = []
})
onMounted(() => {
  $g.bus.on('openResourcePopup', () => {
    openResourcePopup()
  })
  if (route.query?.taskSource == 'zhrw') {
    tabMenu = [...tabMenu, {
      label: '文本内容',
      key: 'TextContent',
    }]
  }
})
defineExpose({ checkFileList })
function getCount(type) {
  switch (type) {
    case 'QiMingResource':
      return checkedQiMingResource.length
    case 'XiaoBenResource':
      return checkedXiaoBenResource.length
    default:
      return fileList.length
  }
}
/* 根据文件类型返回对应图片地址 */
function getFileTypeUrl(ext) {
  let type = ext ? ext.toLowerCase() : null
  if (['doc', 'docx'].includes(type))
    return $g.tool.getFileUrl('taskCenter/docx.png')

  if (['xls', 'xlsx'].includes(type))
    return $g.tool.getFileUrl('taskCenter/excel.png')

  if (['pdf',
'ppt',
'pptx'].includes(type))
    return $g.tool.getFileUrl('taskCenter/pdf.png')

  if (['mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8'].includes(type))
    return $g.tool.getFileUrl('taskCenter/video.png')

  if (['jpg',
'jpeg',
'png',
'gif',
'bmp',
'svg'].includes(type))
    return $g.tool.getFileUrl('taskCenter/img.png')

  return $g.tool.getFileUrl('taskCenter/default-file.png')
}
/* 根据文件类型禁用加入/预览 */
function disablePreview(row) {
  // 文档、图片、视频、音频
  return row.fileExtension && ['doc',
'docx',
'xls',
'xlsx',
'pdf',
'ppt',
'pptx',
'jpg',
'jpeg',
'png',
'gif',
'bmp',
'svg',
'mp4',
'mov',
'avi',
'rmvb',
'flv',
'wmv',
'mkv',
'm3u8'].includes(row.fileExtension)
}
</script>

<template>
  <SubjectAndModel />
  <div class="flex-1 overflow-hidden pb-10px ">
    <div class="flex items-center justify-between mb-12px">
      <span class="text-17px leading-[24px] font-600">{{ route.query?.taskSource == 'zhrw' ? '配置任务' : '4.任务内容' }}</span>
    </div>
    <div class="h-[calc(100%-36px)]">
      <div class="flex justify-between">
        <!-- 资源类型 -->
        <div class="flex">
          <el-badge
            v-for="(item, index) in tabMenu"
            :key="item.key"
            :is-dot="getCount(item.key)"
            class="menu_item flex-cc select-none"
            :class="{
              'mx-10px': index != 0 && index != tabMenu.length - 1,
              'menu-item-active': activeMenu == item.key,
            }"
            @click="handleChangeTab(item.key)"
          >
            <div>{{ item.label }}</div>
          </el-badge>
        </div>
        <!-- 筛选/搜索 -->
        <div v-if="['QiMingResource', 'XiaoBenResource'].includes(activeMenu)" class="flex items-center">
          <div
            v-if="activeMenu == 'QiMingResource'"
            class="text-13px  mr-10px flex cursor-pointer"
            :class="[conditionCount ? 'text-[#6474FD]' : 'text-[#999]']"
            @click="showDialog = true"
          >
            <svg-common-filter class="w-17px h-17px mr-2px" />
            <div>
              筛选<span v-if="conditionCount">({{ conditionCount }})</span>
            </div>
          </div>
          <div>
            <el-input
              v-model="keyword"
              class="w-166px h-34px"
              placeholder="搜索资源"
              @keydown.enter="search"
            >
              <template #suffix>
                <img
                  :src="$g.tool.getFileUrl('taskCenter/search.png')"
                  class="w-17px h-17px"
                  @click="search"
                />
              </template>
            </el-input>
          </div>
        </div>
      </div>
      <div class="my-12px h-[calc(100%-36px-12px)] min-h-[280px]">
        <!-- 上传资源 -->
        <div
          v-if="activeMenu == 'UploadResource'"
        >
          <!-- class="bg-white h-full py-17px px-21px" -->
          <!-- <UploadFile v-model:file-list="fileList" /> -->
          <LocalTask v-model:file-list="fileList" :add-btn-disabled="addBtnDisabled" />
        </div>
        <div v-else-if="activeMenu == 'TextContent'">
          <el-input
            v-model="studentData.textContent"
            :rows="15"
            type="textarea"
            maxlength="500"
            show-word-limit
          ></el-input>
        </div>
        <!-- 启鸣资源/校本资源 -->
        <div v-else-if="activeMenu != 'UploadResource'" class="flex  !max-h-[500px]">
          <div
            class="w-[291px] overflow-hidden bg-white p-15px mr-15px border-[1px] border-[#DADDE8] border-solid rounded-[6px]"
          >
            <g-loading v-if="showLoading" class="h-200px"></g-loading>
            <template v-else>
              <!-- 书籍 -->
              <div v-if="!showTree" class="h-full overflow-y-auto">
                <g-empty v-if="!bookList.length"></g-empty>
                <div
                  v-for="item in bookList"
                  :id="item.sysTextbooksId"
                  :key="item.sysTextbooksId"
                  class="text-14px text-[#333] font-600 py-6px px-7px flex items-start justify-between cursor-pointer rounded-[6px] mb-9px"
                  :class="{
                    'text-[#6474FD] bg-[rgba(100,116,253,0.1)]':
                      currentBookId == item.sysTextbooksId,
                  }"
                  @click="handleChangeBook(item)"
                >
                  <div>{{ item.label }}</div>
                  <img
                    :src="
                      currentBookId == item.sysTextbooksId
                        ? $g.tool.getFileUrl('taskCenter/clicked-right.png')
                        : $g.tool.getFileUrl('taskCenter/not-clicked-right.png')
                    "
                    class="w-19px h-19px mt-2px"
                  />
                </div>
              </div>
              <!-- 具体章节 -->
              <div v-else class="h-full">
                <div class="flex items-start mb-10px">
                  <svg-common-back
                    class="w-[19px] h-19px mt-1px mr-2px cursor-pointer"
                    @click="showTree = false"
                  />
                  <div class="font-600 text-14px">
                    {{ currentBookName }}
                  </div>
                </div>
                <div class="h-[calc(100%-40px)] overflow-auto">
                  <g-tree
                    v-if="treeData.length"
                    ref="Tree2Ref"
                    :border="false"
                    tree-name="RightTree"
                    node-key="sysTextbooksCatalogId"
                    class="text-[13px]"
                    :default-expanded-keys="[currentCategoryId]"
                    :default-checked-keys="[currentCategoryId]"
                    :current-node-key="currentCategoryId"
                    :tree-data="treeData"
                    check-strictly
                    :highlight-check="false"
                    @node-click="nodeClick"
                    @node-expand="
                      () => {
                        $g.tool.renderMathjax()
                      }
                    "
                  >
                    <template #body="{ data }">
                      <div>
                        <g-mathjax :text=" data.sysTextbooksCatalogName" />
                      </div>
                    </template>
                  </g-tree>
                  <g-empty v-else></g-empty>
                </div>
              </div>
            </template>
          </div>
          <div
            class="bg-white flex-1 px-17px py-13px rounded-[6px] overflow-y-auto no-bar "
          >
            <g-loading v-if="showLoading" class="h-200px"></g-loading>
            <template v-else>
              <!-- 年级、资源个数 -->
              <div
                class="h-24px flex items-center text-[#6C6C74] text-13px mb-9px"
              >
                <div class="mt-4px flex-shrink-0">
                  {{ tableOptions.data.length }}个结果
                </div>
                <template v-if="activeMenu == 'QiMingResource'">
                  <div class="w-1px h-7px bg-[#6C6C744D] mx-6px" />
                  <div
                    v-for="(item, index) in checkedCondition"
                    :key="item.type"
                    class="flex items-center"
                  >
                    <template v-if="item.list.length">
                      <div
                        class="h-full px-7px bg-[#E8ECF6] border border-solid border-[#D7DDE9] rounded-[5px] flex items-center"
                      >
                        <div
                          class="max-w-135px truncate mr-4px translate-y-1px"
                        >
                          {{ item.name }}:
                          {{ item.list.map((v) => v.title).join('、') }}
                        </div>
                        <svg-ri-close-line
                          class="text-14px van-haptics-feedback"
                          @click="removeGrade(index + 1)"
                        />
                      </div>
                      <div class="w-1px h-7px bg-[#6C6C744D] mx-6px" />
                    </template>
                  </div>
                  <img
                    src="@/assets/img/taskCenter/bin.png"
                    alt="bin icon"
                    class="w-17px h-17px van-haptics-feedback"
                    @click="removeGrade"
                  />
                </template>
              </div>
              <!-- 资源列表 -->
              <g-table
                :table-options="tableOptions"
                :border="false"
                :header-cell-style="headerCellClassName"
              >
                <template #fileName="{ row }">
                  <div class="flex items-center">
                    <div class="mt-2px flex-shrink-0">
                      <img
                        v-if="getFileTypeUrl(row.fileExtension)"
                        :src="getFileTypeUrl(row.fileExtension)"
                        alt=""
                        class="w-17px h-17px"
                      />
                    </div>
                    <g-mathjax :text="row.fileName" class="mx-5px" />
                    <img
                      v-if="$g.tool.isTrue(row.resourceLevelId)"
                      :src="
                        $g.tool.getFileUrl(
                          `taskCenter/${difficultyMap[row.resourceLevelId]}.png`,
                        )
                      "
                      alt=""
                      class="w-[13px] h-[15px] mt-4px flex-shrink-0"
                    />
                  </div>
                </template>
                <template #cz="{ row }">
                  <div class="text-[#6474FD] text-[14px] flex justify-center items-center">
                    <!-- <div
                      class="mr-10px flex-shrink-0 cursor-pointer"
                      :class="{ 'cursor-not-allowed text-[#999]': !disablePreview(row) }"
                      @click="handlePreview(row)"
                    >
                      预览
                    </div> -->
                    <el-button
                      link
                      :disabled=" !disablePreview(row)"
                      type="primary"
                      @click="handlePreview(row)"
                    >
                      预览
                    </el-button>
                    <el-button
                      v-if="!checkedIdList.includes(row.resourceId)"
                      link
                      :disabled="addBtnDisabled || !disablePreview(row) || btnDisabled(row)"
                      type="primary"
                      @click="selected(row)"
                    >
                      加入
                    </el-button>
                    <!-- <div
                      v-if="!checkedIdList.includes(row.resourceId)"
                      :class="{ 'cursor-not-allowed text-[#999]': !disablePreview(row) }"
                      class="cursor-pointer"
                      @click="selected(row)"
                    >
                      加入
                    </div> -->
                    <!-- <div
                      v-else
                      class="!text-[#FF4646] flex-shrink-0 cursor-pointer"
                      @click="selected(row)"
                    >
                      取消加入
                    </div> -->
                    <el-button
                      v-else
                      link
                      class="!text-[#FF4646] flex-shrink-0 cursor-pointer"
                      type="primary"
                      @click="selected(row)"
                    >
                      取消加入
                    </el-button>
                  </div>
                </template>
              </g-table>
            </template>
          </div>
        </div>
      </div>
    </div>
    <!-- 筛选条件 -->
    <ResourceConditions
      v-model:show="showDialog"
      v-model:condition="checkedCondition"
      :type-list="typeList"
      :source-list="sourceList"
      :grade-list="gradeList"
    />
    <!-- 预览文件 -->
    <PreviewFile v-model:show="showFile" :current-file="currentFile" />
    <!-- 加入的资源 -->
    <ResourcePopup
      v-model:show="showPopup"
      :checked-qi-ming-resource="checkedQiMingResource"
      :checked-xiao-ben-resource="checkedXiaoBenResource"
      :file-list="fileList"
      :count="count"
      @selected="selected"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .el-table .el-table__header-wrapper {
    border-radius: 6px;
    height: 42px !important;
    overflow: hidden;
  }
  thead {
    th {
      background-color: rgba(100, 116, 253, 0.1) !important;
    }
  }
}
.grade-item::before {
  content: ' ';
  display: inline-block;
  width: 1px;
  height: 11px;
  background-color: #dddddd;
  margin: 0 6px;
}
.menu_item {
  border: 1px solid #dadde8;
  padding: 0 17px;
  line-height: 34px;
  border-radius: 6px;
  color: #6c6c74;
  background-color: #fbfbfb;
  font-size: 15px;
  cursor: pointer;
}
.menu-item-active {
  border-color: #646ab4;
  color: #6474fd;
  background-color: #ecefff;
}
:deep() {
  .el-tree-node__content {
    height: auto;
    padding: 5px 0px;
    margin-bottom: 5px;
    background-color: transparent !important;
    transition: color 0.1s linear;
    border-radius: 6px;
  }
  .custom-tree-node {
    overflow: hidden;
    white-space: normal;
  }
  .is-checked {
    .el-tree-node__content {
      background-color: rgba(100, 116, 253, 0.1) !important;
      color: #6474fd;
      .expanded {
        color: #6474fd;
      }
      .el-icon {
        color: #6474fd;
      }
    }
    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent !important;
        color: #6c6c74 !important;
        .expanded {
          color: #6c6c74 !important;
        }
        .el-icon {
          color: #676a88 !important;
        }
      }
    }
  }
}
</style>
