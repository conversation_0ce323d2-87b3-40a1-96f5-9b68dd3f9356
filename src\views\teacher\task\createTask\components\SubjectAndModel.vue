<script setup lang="ts">
import { getTaskPattern } from '@/api/comprehensiveTask'

let route = useRoute()
let taskType = $ref<any>('')
let taskTypeList = $ref<Array<{
  label: string
  value: string | number
}>>([])// 测试类型

let taskPatternConfig = inject<Ref<any>>('taskPatternConfig', ref({}))
let taskModel = inject<Ref<any>>('taskModel', ref(null))
let taskTimeInfo = inject<Ref<any>>('taskTimeInfo', ref({}))

let readTime = $ref<any>(null) // 阅读时间
let dictationTime = $ref<any>(null) // 默写时间
async function getTaskPatternApi() {
  const pageType = route.query?.pageType
  if (!pageType) return

  const res = await getTaskPattern({ taskType: pageType })
  taskTypeList = res.map(it => ({
    label: it?.title,
    value: it?.id?.toString(),
  }))
  // if (route?.query?.pattenType) {
  //   taskTypeList = taskTypeList.filter(it => it.value != 1)
  // }
  taskType = route?.query?.pattenType ?? taskTypeList?.[0]?.value ?? ''
}

watch(() => taskType, (newVal) => {
  if (newVal)
    taskModel.value = newVal

  // 阅读模式没有默写时间
  if (newVal == 3)
    dictationTime = null
})
// 复制任务时模式回显
watch(() => taskModel.value, (newVal) => {
  taskType = newVal?.toString() ?? ''
}, { immediate: true })
// 复制任务时时间回显
watch(() => taskTimeInfo.value, (newVal) => {
  const {
    readingTime,
    dictationTime: dictTime,
  } = newVal ?? {}
  readTime = readingTime ? Number(readingTime) / 60 : null
  dictationTime = dictTime ? Number(dictTime) / 60 : null
}, { immediate: true })

watch(() => [readTime, dictationTime], (newVal) => {
  const [readingTime, dictationTime] = newVal
  taskPatternConfig.value = {
    ...taskPatternConfig.value,
    readingTime: readingTime ? Number(readingTime) * 60 : null,
    dictationTime: dictationTime ? Number(dictationTime) * 60 : null,
  }
  Object.keys(taskPatternConfig.value).forEach((key) => {
    if (taskPatternConfig.value[key] === null)
      delete taskPatternConfig.value[key]
  })
})

onMounted(() => {
  getTaskPatternApi()
})
</script>

<template>
  <div>
    <!-- 综合任务 -->
    <template v-if="route.query?.taskSource == 'zhrw'">
      <div v-if="['2', '3'].includes(taskType)" class="flex mb-[17px]" :class="{ 'pb-[17px] px-[17px] bg-[#fff] rounded-bl-[6px] rounded-br-[6px]': route.query?.taskSource == 'zhrw' }">
        <div class="flex items-center mr-[17px] ">
          <div class="mr-[16px] text-[15px] text-[#333]">
            阅读时长
          </div>
          <div class="relative">
            <el-input-number
              v-model="readTime"
              class="w-[300px]"
              :controls="false"
              suffix-icon="el-icon-time"
              placeholder="请输入分钟数"
            />
            <svg-task-time class="w-15px h-15px mr-[6px] absolute top-[8px] right-[10px] text-[#666]" />
          </div>
        </div>
        <div v-if="taskType == 2" class="flex items-center">
          <div class="mr-[16px] text-[15px] text-[#333]">
            默写时长
          </div>
          <div class="relative">
            <el-input-number
              v-model="dictationTime"
              class="w-[300px]"
              :controls="false"
              suffix-icon="el-icon-time"
              placeholder="请输入分钟数"
            />
            <svg-task-time
              class="w-15px h-15px mr-[6px] absolute top-[8px] right-[10px] text-[#666]"
            />
          </div>
        </div>
      </div>
    </template>
    <!-- 资源任务 -->
    <template v-else>
      <div class="mb-17px" :class="{ '!mb-10px': !$g.isPc }">
        <span class="text-17px leading-[24px] font-600">2.科目</span>
      </div>
      <div v-if="route?.query?.subjectName" class="px-[32px] bg-[#ECEFFF]  text-[#6474FD] text-[15px] border border-[#646AB4] rounded-[6px] w-fit h-[34px] leading-[34px] mb-21px" :class="{ '!mb-10px': !$g.isPc }">
        {{ route?.query?.subjectName }}
      </div>
      <div>
        <div class=" mb-17px" :class="{ '!mb-10px': !$g.isPc }">
          <span class="text-17px leading-[24px] font-600">3.模式设置</span>
        </div>
        <div class="p-17px bg-[#fff] rounded-[6px] mb-[21px]" :class="{ '!py-10px !mb-[10px]': !$g.isPc }">
          <div class="flex items-center ">
            <div class="mr-[16px] text-[15px] text-[#333]">
              任务模式
            </div>
            <el-radio-group v-model="taskType">
              <el-radio
                v-for="item in taskTypeList"
                :key="item.value"
                class="!text-[#333] !text-[15px]"
                :value="item.value"
                :disabled="Boolean(route?.query?.pattenType)"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </div>

          <div v-if="['2', '3'].includes(taskType)" class="flex mt-[17px]">
            <div class="flex items-center mr-[17px] ">
              <div class="mr-[16px] text-[15px] text-[#333]">
                阅读时长
              </div>
              <div class="relative">
                <el-input-number
                  v-model="readTime"
                  class="w-[300px]"
                  :controls="false"
                  suffix-icon="el-icon-time"
                  placeholder="请输入分钟数"
                />
                <svg-task-time class="w-15px h-15px mr-[6px] absolute top-[8px] right-[10px] text-[#666]" />
              </div>
            </div>
            <div v-if="taskType == 2" class="flex items-center">
              <div class="mr-[16px] text-[15px] text-[#333]">
                默写时长
              </div>
              <div class="relative">
                <el-input-number
                  v-model="dictationTime"
                  class="w-[300px]"
                  :controls="false"
                  suffix-icon="el-icon-time"
                  placeholder="请输入分钟数"
                />
                <svg-task-time
                  class="w-15px h-15px mr-[6px] absolute top-[8px] right-[10px] text-[#666]"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped>

</style>
