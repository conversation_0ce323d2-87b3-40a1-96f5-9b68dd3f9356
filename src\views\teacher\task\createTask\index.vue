<script setup lang="ts" name="CreateTask">
import type { studentDataObj } from './types'
import {
  createTask,
  getQuestionResource,
  publishResourceTask,
  taskDetail,
} from '@/api/taskCenter'
import CreateTask from './components/CreateTask.vue'
import ExitDialog from './components/ExitDialog.vue'
import QuestionAnalysis from './components/QuestionAnalysis.vue'
import QuestionStatistics from './components/QuestionStatistics.vue'
import SchoolQuestion from './components/SchoolQuestion.vue'
import SchoolQuestionDetail from './components/SchoolQuestionDetail.vue'
import TaskSet from './components/TaskSet.vue'
import { deduplicateByKey, studentListInit, uploadFile } from './tool'

let stepType = $ref(1)
let questionList = ref<any>([])
let resources = ref<any>([])
let currentBook = $ref<any>(null)
let taskData = ref<any>({}) // 第三步的任务信息
let currentSchool = $ref<any>(null)
let showExit = $ref(false)
let promiseObj = $ref<any>()
let studentData = ref<studentDataObj>({
  classList: [], // 自定义已选择班级列表，selectStudentArr或selectGroupArr任中有一个长度不为0，该班级就被返回。
  specialClass: null, // 非自定义班级
  selectStudentList: [], // 所有选择的学生id总和，已去重
  disabledStudentIds: [], // 需要禁止操作的学生id集合
  disabledGroupIds: [], // 需要禁止操作的组id集合
  initLoading: false, // 复制任务时需要loading
  taskType: '', // 综合任务类型:QiMingResource->启鸣资源,XiaoBenResource->校本资源,UploadResource->本地资源,TextContent->文本内容
  textContent: '', // 文本内容
})

let currentPaperId = ref<any>(null)
let submitLoading = $ref(false)
let initClassType = $ref<any>(null)
let taskPatternConfig = $ref<any>({})// 配置内容默写{"readingTime":10,"dictationTime":10}阅题{"readingTime":10}其他类型没有配置内容
let taskModel = ref<any>(null)// 配置模式 标准，举一反三，默写，阅读
let taskTimeInfo = ref({})

const route = useRoute()
const router = useRouter()
// 需要全屏的页面在这里配置
const notShowBottom = $computed(() => {
  return ([] as any).includes(stepType)
})
const isResource = $computed(() => {
  return (route.query.pageType as any) == 3 // 1为学科网选题 2为校本练习选题 3为资源类型
})
provide('getStepVal', () => stepType)
provide('setStepVal', setStepVal)
provide('questionList', questionList)
provide('studentData', studentData)
provide('resources', resources)
provide('setCurrentBook', setCurrentBook)
provide('taskData', taskData)
provide('setCurrentSchool', setCurrentSchool)
provide('currentPaperId', currentPaperId)
provide('taskPatternConfig', taskPatternConfig)
provide('taskModel', taskModel)
provide('taskTimeInfo', taskTimeInfo)
let nextStepDisabled = $computed(() => {
  const { selectStudentList, taskType, textContent } = studentData.value
  if (!selectStudentList.length) { return true }

  if (taskType === 'TextContent') {
    return !textContent
  }

  return !questionList.value.length && !resources.value.length
})

function setStepVal(val) {
  stepType = val
}
function setCurrentBook(val) {
  currentBook = val
}
function setCurrentSchool(val) {
  currentSchool = val
}

function getClassList() {
  let deepCloneArr = JSON.parse(JSON.stringify(studentData.value.classList))
  let classList
  let find = deepCloneArr.find(
    h => h.schoolClassId === studentData.value.specialClass?.schoolClassId,
  )
  if (find) {
    find.selectStudentArr = studentData.value.specialClass.selectStudentArr
    find.arrangeObjectType = 1
    classList = deepCloneArr
  }
  else {
    classList = [
      ...deepCloneArr,
      ...(studentData.value.specialClass
        ? [studentData.value.specialClass]
        : []),
    ]
  }
  let groups = deduplicateByKey(
    classList.flatMap(item => item.selectGroupArr).filter(Boolean),
    'schoolClassId',
  )
  classList = [
    ...classList.filter(v => v.selectStudentArr.length),
    ...groups.map((h) => {
      return {
        ...h,
        arrangeObjectType: 2,
        selectStudentArr: h.list,
      }
    }),
  ]
  return classList.map((v) => {
    return {
      schoolClassId: v.schoolClassId,
      arrangeObjectType: v.arrangeObjectType,
      schoolStudentIds: v.selectStudentArr.map(h => h.schoolStudentId),
    }
  })
}
async function pubQuestion() {
  let params = {
    sysSubjectId: route.query.subjectId,
    taskType: route.query.pageType,
    ...taskData.value,
    releaseTime: route.query.taskSource === 'zhrw' ? undefined : (taskData.value.isImmediate ? null : taskData.value.releaseTime),
    requireCompleteTime: route.query.taskSource === 'zhrw' ? undefined : (taskData.value.isUnlimited ? null : taskData.value.requireCompleteTime),
    copyId,
    questions: questionList.value.map((item) => {
      return {
        bookId: item.bookId,
        bookCatalogId: item.bookCatalogId,
        questionId: item.questionId,
        // 题目选择来源:1 学科网题目，2校本练习题目
        chooseSource: item.chooseSource || 2,
      }
    }),
    taskPattern: {
      type: taskModel.value ?? '',
      config: taskPatternConfig.value ?? {},
    },
    classList: getClassList(),
    classType: createTaskRef?.getRealClassType(),
    taskScheduleGroupId: route.query.taskScheduleGroupId,
  }

  delete params.isImmediate
  delete params.configVideoProgressBar
  delete params.isUnlimited
  delete params.notIsRecommend
  await createTask(params)
}

async function pubResource() {
  let {
    taskName,
    releaseTime,
    requireCompleteTime,
    configVideoProgressBar,
    courseNum,
    teacherMessage,
    estimateTime,
  } = taskData.value
  let params: any = {
    sysSubjectId: route.query.subjectId,
    releaseTime: route.query.taskSource === 'zhrw' ? undefined : (taskData.value.isImmediate ? null : releaseTime),
    taskName,
    requireCompleteTime: route.query.taskSource === 'zhrw' ? undefined : (taskData.value.isUnlimited ? null : requireCompleteTime),
    configVideoProgressBar,
    estimateTime,
    courseNum,
    classList: getClassList(),
    resources: resources.value,
    copyId,
    teacherMessage,
    taskPattern: {
      type: taskModel.value ?? '',
      config: taskPatternConfig.value ?? {},
    },
    classType: createTaskRef?.getRealClassType(),
    taskScheduleGroupId: route.query.taskScheduleGroupId,
  }
  // 配置任务类型为文本内容
  if (studentData.value.taskType == 'TextContent' && studentData.value.textContent) {
    let res = await uploadFile(studentData.value.textContent)
    params.resources = [{ resourceSource: 5, fileAbsoluteUrl: res?.resource_url ?? '', fileName: $g._.uniqueId('文本内容_') }]
  }
  await publishResourceTask(params)
}

async function publish() {
  if (
    !taskData.value.taskName ||
    !taskData.value.releaseTime ||
    !taskData.value.requireCompleteTime
  ) {
    $g.showToast('请输入任务名称和任务时间')
    return
  }
  submitLoading = true
  try {
    if (isResource)
      await pubResource()

    else
      await pubQuestion()

    if (route.query.taskSource === 'zhrw') {
      localStorage.setItem('notShowExit', 'true')
      router.back()
      $g.bus.emit('updateSubList')
      return
    }
    $g.showToast('布置成功，您可以在任务列表中查看此任务')
    if ($g.isPC) {
      router.replace({
        name: 'TeacherHome',
        query: {
          notShowExit: 'true',
        },
      })
    }
    else {
      $g.flutter('back', true)
    }
  }
  catch (e) {
    console.log(e)
  }
  finally {
    submitLoading = false
  }
}
function handleBeforeUnload(event: any) {
  if (
    !questionList.value.length &&
    !resources.value.length &&
    !studentData.value.selectStudentList.length
  ) 
    return


  let confirmationMessage
  confirmationMessage = '当前数据还未保存，刷新页面将会导致数据丢失，是否刷新？'
  event.returnValue = confirmationMessage
  return confirmationMessage
}

let copyId
async function initData() {
  if (route.query.taskId) {
    studentData.value.initLoading = true
    try {
      let resArr = await Promise.all([
        taskDetail({
          taskId: route.query.taskId,
        }),
        getQuestionResource({
          taskId: route.query.taskId,
        }),
      ])

      questionList.value = resArr[1].questions
      resources.value = resArr[1].resources
      taskModel.value = resArr[0]?.taskPattern?.taskPatternType
      taskTimeInfo.value = JSON.parse(resArr[0]?.taskPattern?.taskPatternValue || '{}')
      console.log('  taskTimeInfo.value=> ', taskTimeInfo.value)
      let studentObj = studentListInit(resArr[0])
      studentData.value.classList = studentObj.classList
      studentData.value.selectStudentList = studentObj.selectStudentList
      initClassType = studentData.value.classList[0].classType
      setTimeout(() => {
        $g.msg(
          `<div>已创建<span style="color:#6474FD;margin:0 3px 0 3px">${route.query.taskName}</span>的副本</div>`,
        )
        let updateQuery = {
          ...route.query,
        }
        copyId = updateQuery.taskId
        delete updateQuery.taskId
        delete updateQuery.taskName
        router.replace({
          query: updateQuery,
        })
      }, 0)
    }
    catch (e) {
      console.log(e)
    }
    finally {
      studentData.value.initLoading = false
    }
  }
}

let createTaskRef = $ref<any>(null)
/* 下一步 */
function handleNextStep() {
  // 本地资源上传，不需要教研时候上传完成
  // if ((route.query.pageType as any) == 3) {
  //   let status = createTaskRef.checkFileList()
  //   if (status) {
  //     return false
  //   }
  // }
  stepType = 3
}

onBeforeMount(async () => {
  initData()
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onBeforeRouteLeave((to, from, next) => {
  // 综合任务最后一步离开页面时不需要弹出弹窗
  const zhrwShowExit = localStorage.getItem('notShowExit')
  if (to.query.notShowExit || zhrwShowExit) {
    next()
    return
  }
  showExit = true
  new Promise((resolve, reject) => {
    promiseObj = {
      resolve,
      reject,
    }
  }).then(() => {
    next()
  }).catch(() => {
      next(false)
    })
})
onBeforeUnmount(() => {
  localStorage.removeItem('notShowExit')
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>

<template>
  <div>
    <!-- 不需要底部的直接放到这里 -->
    <div v-if="notShowBottom" />

    <!-- 有底部 -->
    <div v-else class="h-[100vh] flex flex-col">
      <div class="flex-1 h-0 overflow-auto no-bar p-26px pb-0">
        <CreateTask v-show="stepType === 1"
                    ref="createTaskRef"
                    :class-type="initClassType"
        />
        <SchoolQuestion v-show="stepType === 2" :step-type="stepType" />
        <TaskSet v-show="stepType === 3"></TaskSet>
        <SchoolQuestionDetail
          v-if="stepType === 4"
          :school="currentSchool"
          :book="currentBook"
        />
        <QuestionStatistics v-if="stepType === 5" />
      </div>
      <!-- 底部 -->
      <div
        class="bg-[#FFFFFF] h-[9vh] w-full flex-shrink-0 pr-26px"
        style="box-shadow: 0px -2px 7px 0px rgba(0, 0, 0, 0.04)"
      >
        <div class="flex items-center h-full justify-end">
          <div
            v-if="!isResource && stepType !== 3"
            class="mr-17px text-15px font-600 text-[#666666]"
          >
            已添加<span class="text-[#6474FD] mx-4px">
              {{
                questionList.length
              }}
            </span>题
          </div>

          <div v-if="![2, 4, 5].includes(stepType) && route.query.taskSource != 'zhrw'" class="flex">
            <el-button
              class="mr-13px text-15px h-30px text-[#666666] border-[#CCCCCC]"
              plain
              @click="showExit = true"
            >
              取消布置
            </el-button>
          </div>

          <div v-if="stepType === 1" class="flex">
            <QuestionAnalysis v-if="!isResource" />
            <el-button
              v-else
              v-show="studentData.taskType != 'TextContent'"
              :disabled="!resources.length "
              @click="$g.bus.emit('openResourcePopup')"
            >
              已加入({{ resources.length }})
            </el-button>
            <el-button
              class="ml-13px text-15px font-600 h-30px"
              type="primary"
              :disabled="
                nextStepDisabled
              "
              @click="handleNextStep"
            >
              下一步
            </el-button>
          </div>

          <div v-if="[2, 4, 5].includes(stepType)" class="flex">
            <el-button
              class="text-15px font-600 h-30px"
              type="primary"
              @click="stepType = 1"
            >
              去布置
            </el-button>
          </div>

          <div v-if="stepType === 3" class="flex">
            <el-button
              class="h-30px bg-[#ECEFFF] text-[#5864F8] border-[#646AB4] text-15px font-600"
              @click="stepType = 1"
            >
              上一步
            </el-button>
            <el-button
              class="ml-13px text-15px font-600 h-30px"
              type="primary"
              :loading="submitLoading"
              @click="publish"
            >
              完成并布置
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <ExitDialog v-model:show="showExit" :promise-obj="promiseObj"></ExitDialog>
  </div>
</template>

<style scoped lang="scss"></style>
